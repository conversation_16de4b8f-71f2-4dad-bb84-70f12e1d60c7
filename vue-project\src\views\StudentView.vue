<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import PageHeader from '../components/PageHeader.vue'
import StudentQuickResponsePanel from '../components/StudentQuickResponsePanel.vue'
import SlideAnnotationsPanel from '../components/SlideAnnotationsPanel.vue'
import { getCurrentSlide, getTotalSlides, getPPTInfo, trackStudentAttention } from '../services/db/classroomService'
import { getVotes, hasVoted, submitVote } from '../services/db/voteService'
// 引入Socket通信服务
import { 
  initSocket, 
  getSocket, 
  socketStatus, 
  controlStatus,
  classroomStatus,
  sendSlideChange,
  sendQuestion,
  submitVote as socketSubmitVote,
  joinClassroom,
  leaveClassroom
} from '../services/socketService'

// Initialize router
const router = useRouter()

// 获取当前用户信息
const currentUser = ref({
  id: localStorage.getItem('userId') || '',
  name: localStorage.getItem('userName') || '未知用户',
  avatar: localStorage.getItem('userAvatar') || '',
  role: localStorage.getItem('userRole') || ''
})

// 加入课堂相关状态
const joinClassCodeInput = ref('') // 输入的课堂码

// 计算属性：是否有控制权
const hasControl = computed(() => {
  const isGranted = controlStatus.isControlGranted;
  const userMatch = controlStatus.controllingUserId === currentUser.value.id;
  const hasFullAccess = controlStatus.hasFullAccess;
  
  // 添加调试信息
  console.log('权限检查详情:', {
    是否授权: isGranted,
    用户匹配: userMatch,
    完全访问: hasFullAccess,
    控制用户ID: controlStatus.controllingUserId,
    当前用户ID: currentUser.value.id,
    最终结果: isGranted && (userMatch || hasFullAccess)
  });
  
  // 简化权限逻辑：已授权且（用户匹配或有完全访问权限）
  return isGranted && (userMatch || hasFullAccess);
});

// 当前活动工具
const currentTool = ref('cursor')

// 所有可用的工具
const tools = [
  { name: 'cursor', icon: 'Pointer', label: '光标' },
  { name: 'pen', icon: 'EditPen', label: '笔' },
  { name: 'line', icon: 'Minus', label: '直线' },
  { name: 'rect', icon: 'Grid', label: '矩形' },
  { name: 'circle', icon: 'CirclePlus', label: '圆形' },
  { name: 'text', icon: 'Document', label: '文本' },
  { name: 'eraser', icon: 'Delete', label: '橡皮擦' }
]

// 选择工具
const selectTool = (tool) => {
  currentTool.value = tool
  console.log('选择工具:', tool)
  ElMessage.success(`已切换到 ${tools.find(t => t.name === tool)?.label} 工具`)
  
  // 根据工具设置不同的鼠标光标
  if (whiteboard.value) {
    switch (tool) {
      case 'cursor':
        whiteboard.value.style.cursor = 'default';
        break;
      case 'pen':
        whiteboard.value.style.cursor = 'crosshair';
        break;
      case 'line':
      case 'rect':
      case 'circle':
        whiteboard.value.style.cursor = 'crosshair';
        break;
      case 'text':
        whiteboard.value.style.cursor = 'text';
        break;
      case 'eraser':
        whiteboard.value.style.cursor = 'grab';
        break;
      default:
        whiteboard.value.style.cursor = 'crosshair';
    }
  }
}

// 当前PPT页面
const currentSlide = ref(1)
const totalSlides = ref(10)

// 白板相关变量
const whiteboard = ref(null)
const isDrawing = ref(false)
const lastX = ref(0)
const lastY = ref(0)
const startX = ref(0) // 绘制起点X
const startY = ref(0) // 绘制起点Y
const tempCanvas = ref(null) // 临时画布用于预览
const textInput = ref('') // 文本输入
const isTyping = ref(false) // 是否在输入文本
const drawingColor = ref('#f56c6c') // 绘图颜色
const drawingWidth = ref(2) // 绘图线宽
// PPT信息对象
const pptInfo = ref({
  id: '',
  title: '加载中...',
  slideCount: 10,
  currentIndex: 1,
  images: []
})

// 初始化PPT数据
const initPPTData = async () => {
  try {
    // 获取当前页码
    currentSlide.value = await getCurrentSlide()
    // 获取总页数
    totalSlides.value = await getTotalSlides()
    // 获取PPT详细信息
    const pptData = await getPPTInfo()
    pptInfo.value = pptData
  } catch (error) {
    console.error('初始化PPT数据错误:', error)
    ElMessage.error('获取PPT数据失败')
  }
}

// 侧边抽屉状态
const quickResponseVisible = ref(false)
const annotationsVisible = ref(false)

// 追踪学生注意力状态
const trackAttention = () => {
  // 实际项目中应该通过用户行为来判断
  const isFocused = document.visibilityState === 'visible'
  trackStudentAttention(currentUser.value.id, isFocused)
}

// 视图可见性变化处理
const handleVisibilityChange = () => {
  trackAttention()
}

// 问题列表
const questions = ref([])
// 加载学生问题
const loadQuestions = async () => {
  try {
    // 实际项目中应该从数据库加载
    questions.value = [
      { id: 1, content: '这个公式怎么理解？', answered: false },
      { id: 2, content: '上节课的内容还有疑问', answered: true }
    ]
  } catch (error) {
    console.error('加载问题失败:', error)
  }
}

// 笔记对话框状态
const notesVisible = ref(false)
// 问题对话框状态
const questionsVisible = ref(false)
// 练习对话框状态
const exercisesVisible = ref(false)
// 添加缺失的文本输入变量
const newQuestion = ref('')
const noteTitle = ref('')
const noteContent = ref('')

// 触摸滑动相关变量
let touchStartX = 0
let touchEndX = 0

// 触摸滑动处理函数
const handleTouchStart = (e) => {
  touchStartX = e.touches[0].clientX
}

const handleTouchMove = (e) => {
  touchEndX = e.touches[0].clientX
}

const handleTouchEnd = () => {
  // 计算滑动距离
  const swipeDistance = touchEndX - touchStartX
  
  // 判断滑动方向并执行相应翻页操作
  if (swipeDistance > 75) {
    // 向右滑动，上一页
    prevSlide()
  } else if (swipeDistance < -75) {
    // 向左滑动，下一页
    nextSlide()
  }
}

// PPT翻页函数
const prevSlide = () => {
  // 检查是否有控制权
  if (!hasControl.value) {
    ElMessage.warning('您没有控制权，无法切换幻灯片');
    return;
  }
  
  if (currentSlide.value > 1) {
    currentSlide.value--;
    
    // 发送页面变化通知
    sendSlideChange({
      slideIndex: currentSlide.value,
      totalSlides: totalSlides.value
    });
  }
}

const nextSlide = () => {
  // 检查是否有控制权
  if (!hasControl.value) {
    ElMessage.warning('您没有控制权，无法切换幻灯片');
    return;
  }
  
  if (currentSlide.value < totalSlides.value) {
    currentSlide.value++;
    
    // 发送页面变化通知
    sendSlideChange({
      slideIndex: currentSlide.value,
      totalSlides: totalSlides.value
    });
  }
}

// 投票相关
const activeVotes = ref([])

// 加载活动中的投票
const loadActiveVotes = async () => {
  try {
    // 只获取活动中的投票
    const votes = await getVotes({ status: 'active' })
    
    // 检查每个投票的参与状态
    for (const vote of votes) {
      vote.hasVoted = await hasVoted(vote.id, currentUser.value.id)
    }
    
    // 更新活动投票列表
    activeVotes.value = votes
    
    // 检查是否有新投票，如果有，则提示用户
    checkForNewVotes(votes)
  } catch (error) {
    console.error('加载投票失败:', error)
  }
}

// 上次检查的投票ID列表
const lastCheckedVoteIds = ref([])

// 检查是否有新投票
const checkForNewVotes = (votes) => {
  // 过滤出活动中且用户未参与的投票
  const newActiveVotes = votes.filter(vote => 
    vote.status === 'active' && 
    !vote.hasVoted && 
    !lastCheckedVoteIds.value.includes(vote.id)
  )
  
  // 如果有新投票，则提示用户
  if (newActiveVotes.length > 0) {
    // 更新已检查的投票ID列表
    newActiveVotes.forEach(vote => {
      if (!lastCheckedVoteIds.value.includes(vote.id)) {
        lastCheckedVoteIds.value.push(vote.id)
      }
    })
    
    // 显示新投票提示
    if (newActiveVotes.length === 1) {
      const vote = newActiveVotes[0]
      ElMessageBox.confirm(
        `教师发起了一个新投票: "${vote.title}"。是否立即参与？`,
        '收到新投票',
        {
          confirmButtonText: '立即参与',
          cancelButtonText: '稍后再说',
          type: 'info'
        }
      ).then(() => {
        currentVote.value = vote
        voteDialogVisible.value = true
      }).catch(() => {})
    } else {
      ElMessage({
        message: `收到 ${newActiveVotes.length} 个新投票，请查看投票面板参与！`,
        type: 'info',
        duration: 5000
      })
    }
  }
}

// 投票相关状态
const voteDialogVisible = ref(false)
const currentVote = ref(null)
const selectedOptions = ref([])

// 处理投票选项选择
const handleOptionSelect = (optionIndex) => {
  if (!currentVote.value.isMultiple) {
    selectedOptions.value = [optionIndex]
  } else {
    // 多选逻辑
    const index = selectedOptions.value.indexOf(optionIndex)
    if (index === -1) {
      selectedOptions.value.push(optionIndex)
    } else {
      selectedOptions.value.splice(index, 1)
    }
  }
}

// 提交投票
const submitVoteAnswer = async () => {
  if (!selectedOptions.value.length) {
    ElMessage.warning('请选择至少一个选项')
    return
  }
  
  try {
    // 创建投票结果对象
    const voteResult = {
      voteId: currentVote.value.id,
      userId: currentUser.value.id,
      userName: currentUser.value.name,
      optionIds: selectedOptions.value,
      anonymous: false,
      submittedAt: new Date().toISOString()
    }
    
    console.log('提交投票:', voteResult);
    
    // 通过Socket发送投票结果
    socketSubmitVote(voteResult)
    
    // 更新本地状态
    const voteIndex = activeVotes.value.findIndex(v => v.id === currentVote.value.id);
    if (voteIndex !== -1) {
      activeVotes.value[voteIndex].hasVoted = true;
    }
    
    // 关闭对话框并重置
    voteDialogVisible.value = false
    selectedOptions.value = []
    
    // 不再显示成功消息，由socket事件处理
  } catch (error) {
    console.error('提交投票失败:', error)
    ElMessage.error('投票提交失败')
  }
}

// 发送提问
const askQuestion = () => {
  questionsVisible.value = true;
}

// 提交问题
const submitQuestion = (question) => {
  if (!question.trim()) {
    ElMessage.warning('问题内容不能为空');
    return;
  }
  
  // 创建问题对象
  const questionData = {
    userId: currentUser.value.id,
    userName: currentUser.value.name,
    content: question,
    time: new Date().toISOString(),
    answered: false
  };
  
  // 通过Socket发送问题
  sendQuestion(questionData);
  
  ElMessage.success('问题已提交');
  questionsVisible.value = false;
  
  // 添加到本地问题列表
  questions.value.unshift(questionData);
}

// 提交课堂码，加入课堂
const handleJoinClassroom = () => {
  if (!joinClassCodeInput.value) {
    ElMessage.warning('请输入课堂码');
    return;
  }

  joinClassroom(joinClassCodeInput.value.trim().toUpperCase());
}

// 请求课堂问题历史
const requestQuestionHistory = (classCode) => {
  const socket = getSocket();
  if (socket) {
    socket.emit('get-classroom-questions', { classCode });
  }
}

// 退出当前课堂
const handleLeaveClassroom = () => {
  ElMessageBox.confirm(
    '确定要退出当前课堂吗？',
    '退出课堂',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    leaveClassroom();
  }).catch(() => {});
}

// 清空白板
const clearWhiteboard = () => {
  if (!whiteboard.value) {
    ElMessage.warning('白板未初始化');
    return;
  }
  
  const context = whiteboard.value.getContext('2d')
  context.clearRect(0, 0, whiteboard.value.width, whiteboard.value.height)
  
  // 如果有临时画布，也清空它
  if (tempCanvas.value) {
    const tempContext = tempCanvas.value.getContext('2d')
    tempContext.clearRect(0, 0, tempCanvas.value.width, tempCanvas.value.height)
  }
  
  console.log('白板已清空');
  ElMessage.success('白板已清空');
}

// 测试绘制功能
const testDrawing = () => {
  if (!whiteboard.value) {
    ElMessage.warning('白板未初始化');
    return;
  }
  
  console.log('开始测试绘制功能...');
  console.log('当前工具:', currentTool.value);
  console.log('白板尺寸:', whiteboard.value.width, 'x', whiteboard.value.height);
  console.log('临时画布存在:', !!tempCanvas.value);
  
  // 绘制一个测试图形
  const context = whiteboard.value.getContext('2d');
  context.strokeStyle = '#ff0000';
  context.lineWidth = 3;
  context.lineCap = 'round';
  
  context.beginPath();
  context.moveTo(50, 50);
  context.lineTo(150, 150);
  context.stroke();
  
  ElMessage.success('测试绘制完成 - 如果您看到红色斜线，说明绘制功能正常');
}

// 检查权限状态
const checkPermissionStatus = () => {
  const status = {
    当前用户: currentUser.value.name,
    用户ID: currentUser.value.id,
    权限状态: controlStatus.isControlGranted ? '已授权' : '未授权',
    控制用户ID: controlStatus.controllingUserId,
    控制用户名: controlStatus.controllingUserName,
    完全访问: controlStatus.hasFullAccess ? '是' : '否',
    最终权限: hasControl.value ? '有权限' : '无权限'
  };
  
  console.table(status);
  
  ElNotification({
    title: '权限状态检查',
    message: `
      当前用户: ${status.当前用户}
      权限状态: ${status.权限状态}
      控制用户: ${status.控制用户名 || '无'}
      最终权限: ${status.最终权限}
    `,
    type: hasControl.value ? 'success' : 'warning',
    duration: 8000,
    position: 'bottom-right'
  });
}

// 强制刷新权限状态
const refreshPermissionStatus = () => {
  // 手动触发权限检查
  console.log('手动刷新权限状态...');
  
  // 重新获取用户信息
  currentUser.value.id = localStorage.getItem('userId') || '';
  currentUser.value.name = localStorage.getItem('userName') || '未知用户';
  
  // 检查权限状态
  checkPermissionStatus();
  
  ElMessage.info('权限状态已刷新，请查看通知详情');
}

// 临时强制授权（用于调试）
const forceGrantPermission = () => {
  ElMessageBox.confirm(
    '这是一个临时调试功能，将强制授予您控制权。确定要继续吗？',
    '临时授权',
    {
      confirmButtonText: '确定授权',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 强制设置权限状态
    controlStatus.isControlGranted = true;
    controlStatus.controllingUserId = currentUser.value.id;
    controlStatus.controllingUserName = currentUser.value.name;
    controlStatus.hasFullAccess = true;
    
    console.log('强制授权成功，权限状态:', {
      isControlGranted: controlStatus.isControlGranted,
      controllingUserId: controlStatus.controllingUserId,
      hasFullAccess: controlStatus.hasFullAccess
    });
    
    ElMessage.success('临时权限已授予，您现在可以使用绘图工具');
    ElNotification({
      title: '临时权限授予成功',
      message: '您现在拥有完全控制权，可以使用所有绘图工具',
      type: 'success',
      duration: 5000
    });
  }).catch(() => {
    ElMessage.info('已取消授权操作');
  });
}

// 创建临时画布
const createTempCanvas = () => {
  if (!tempCanvas.value) {
    tempCanvas.value = document.createElement('canvas')
    tempCanvas.value.width = whiteboard.value.width
    tempCanvas.value.height = whiteboard.value.height
    
    // 设置完整的样式
    tempCanvas.value.style.position = 'absolute'
    tempCanvas.value.style.top = '0'
    tempCanvas.value.style.left = '0'
    tempCanvas.value.style.pointerEvents = 'none'
    tempCanvas.value.style.zIndex = '11' // 确保在主画布之上
    tempCanvas.value.style.background = 'transparent'
    
    // 设置绘图上下文样式
    const tempContext = tempCanvas.value.getContext('2d')
    tempContext.lineCap = 'round'
    tempContext.lineJoin = 'round'
    
    whiteboard.value.parentNode.appendChild(tempCanvas.value)
    console.log('临时画布已创建，尺寸:', tempCanvas.value.width, 'x', tempCanvas.value.height)
  }
}

// 鼠标按下事件
const handleMouseDown = (e) => {
  console.log('鼠标按下，当前工具:', currentTool.value, '控制权:', hasControl.value);
  
  // 检查是否有控制权
  if (!hasControl.value) {
    // 完全移除权限警告，用户已经知道自己的权限状态
    return;
  }
  
  // 处理文本工具的特殊情况
  if (currentTool.value === 'text') {
    isTyping.value = true;
    startX.value = e.offsetX;
    startY.value = e.offsetY;
    console.log('文本工具激活，位置:', startX.value, startY.value);
    return;
  }
  
  // 其他绘图逻辑
  isDrawing.value = true;
  lastX.value = e.offsetX;
  lastY.value = e.offsetY;
  startX.value = e.offsetX;
  startY.value = e.offsetY;
  
  console.log('开始绘制，起点:', startX.value, startY.value);
  
  // 使用临时画布预览
  if (currentTool.value !== 'pen' && currentTool.value !== 'cursor' && currentTool.value !== 'eraser') {
    createTempCanvas();
  }
};

// 鼠标移动事件
const handleMouseMove = (e) => {
  if (!isDrawing.value || !hasControl.value) return
  
  // 统一使用offsetX和offsetY获取相对于canvas的坐标
  const x = e.offsetX
  const y = e.offsetY
  
  if (currentTool.value === 'cursor') {
    // 光标模式下不绘制
    return
  } else if (currentTool.value === 'pen') {
    // 自由绘制模式，直接在主画布上绘制
    drawPenStroke(lastX.value, lastY.value, x, y)
  } else if (currentTool.value === 'eraser') {
    // 橡皮擦模式
    erase(x, y)
  } else {
    // 其他工具使用临时画布进行预览
    if (tempCanvas.value) {
      const tempContext = tempCanvas.value.getContext('2d')
      tempContext.clearRect(0, 0, tempCanvas.value.width, tempCanvas.value.height)
      
      switch (currentTool.value) {
        case 'line':
          drawLinePreview(startX.value, startY.value, x, y)
          break
        case 'rect':
          drawRectPreview(startX.value, startY.value, x, y)
          break
        case 'circle':
          drawCirclePreview(startX.value, startY.value, x, y)
          break
      }
    }
  }
  
  lastX.value = x
  lastY.value = y
}

// 鼠标释放事件
const handleMouseUp = (e) => {
  if (!isDrawing.value) return
  
  // 使用offsetX和offsetY，如果事件没有这些属性（比如mouseleave），则使用最后记录的位置
  const x = e.offsetX !== undefined ? e.offsetX : lastX.value
  const y = e.offsetY !== undefined ? e.offsetY : lastY.value
  
  if (currentTool.value !== 'pen' && currentTool.value !== 'cursor' && currentTool.value !== 'eraser') {
    // 从临时画布复制到主画布
    const context = whiteboard.value.getContext('2d')
    
    switch (currentTool.value) {
      case 'line':
        drawLine(startX.value, startY.value, x, y)
        break
      case 'rect':
        drawRect(startX.value, startY.value, x, y)
        break
      case 'circle':
        drawCircle(startX.value, startY.value, x, y)
        break
    }
    
    // 清空临时画布
    if (tempCanvas.value) {
      const tempContext = tempCanvas.value.getContext('2d')
      tempContext.clearRect(0, 0, tempCanvas.value.width, tempCanvas.value.height)
    }
  }
  
  isDrawing.value = false
  console.log('绘制结束，工具:', currentTool.value)
}

// 绘制直线
const drawLine = (x1, y1, x2, y2) => {
  const context = whiteboard.value.getContext('2d')
  context.beginPath()
  context.moveTo(x1, y1)
  context.lineTo(x2, y2)
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.lineCap = 'round'
  context.stroke()
  
  console.log('绘制直线从', x1, y1, '到', x2, y2)
  
  // 广播绘图操作到服务器
  broadcastDrawing('line', {
    x1, y1, x2, y2,
    color: drawingColor.value,
    width: drawingWidth.value
  })
}

// 绘制笔划（自由绘制）
const drawPenStroke = (x1, y1, x2, y2) => {
  const context = whiteboard.value.getContext('2d')
  
  // 设置绘图样式
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.lineCap = 'round'
  context.lineJoin = 'round'
  
  context.beginPath()
  context.moveTo(x1, y1)
  context.lineTo(x2, y2)
  context.stroke()
  
  console.log('笔划绘制从', x1, y1, '到', x2, y2)
  
  // 广播绘图操作到服务器
  broadcastDrawing('pen', {
    x1, y1, x2, y2,
    color: drawingColor.value,
    width: drawingWidth.value
  })
}

// 预览直线
const drawLinePreview = (x1, y1, x2, y2) => {
  const context = tempCanvas.value.getContext('2d')
  
  // 设置绘图样式
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.lineCap = 'round'
  context.lineJoin = 'round'
  
  context.beginPath()
  context.moveTo(x1, y1)
  context.lineTo(x2, y2)
  context.stroke()
  
  console.log('预览直线从', x1, y1, '到', x2, y2)
}

// 绘制矩形
const drawRect = (x1, y1, x2, y2) => {
  const context = whiteboard.value.getContext('2d')
  context.beginPath()
  context.rect(
    Math.min(x1, x2),
    Math.min(y1, y2),
    Math.abs(x2 - x1),
    Math.abs(y2 - y1)
  )
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.stroke()
  
  console.log('绘制矩形从', x1, y1, '到', x2, y2)
  
  // 广播绘图操作到服务器
  broadcastDrawing('rect', {
    x1, y1, x2, y2,
    color: drawingColor.value,
    width: drawingWidth.value
  })
}

// 预览矩形
const drawRectPreview = (x1, y1, x2, y2) => {
  const context = tempCanvas.value.getContext('2d')
  
  // 设置绘图样式
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.lineCap = 'round'
  context.lineJoin = 'round'
  
  context.beginPath()
  context.rect(
    Math.min(x1, x2),
    Math.min(y1, y2),
    Math.abs(x2 - x1),
    Math.abs(y2 - y1)
  )
  context.stroke()
  
  console.log('预览矩形从', x1, y1, '到', x2, y2)
}

// 绘制圆形
const drawCircle = (x1, y1, x2, y2) => {
  const context = whiteboard.value.getContext('2d')
  const radius = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2))
  
  context.beginPath()
  context.arc(x1, y1, radius, 0, 2 * Math.PI)
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.stroke()
  
  console.log('绘制圆形，中心:', x1, y1, '半径:', radius)
  
  // 广播绘图操作到服务器
  broadcastDrawing('circle', {
    x: x1, 
    y: y1, 
    radius,
    color: drawingColor.value,
    width: drawingWidth.value
  })
}

// 预览圆形
const drawCirclePreview = (x1, y1, x2, y2) => {
  const context = tempCanvas.value.getContext('2d')
  const radius = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2))
  
  // 设置绘图样式
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.lineCap = 'round'
  context.lineJoin = 'round'
  
  context.beginPath()
  context.arc(x1, y1, radius, 0, 2 * Math.PI)
  context.stroke()
  
  console.log('预览圆形，中心:', x1, y1, '半径:', radius)
}

// 提交文本输入
const commitTextInput = () => {
  if (isTyping.value && textInput.value.trim()) {
    const context = whiteboard.value.getContext('2d')
    context.font = '16px Arial'
    context.fillStyle = drawingColor.value
    context.fillText(textInput.value, startX.value, startY.value)
    
    console.log('添加文本:', textInput.value, '位置:', startX.value, startY.value)
    ElMessage.success('文本已添加')
    
    // 广播绘图操作到服务器
    broadcastDrawing('text', {
      x: startX.value,
      y: startY.value,
      text: textInput.value,
      color: drawingColor.value,
      font: '16px Arial'
    })
  } else if (isTyping.value) {
    ElMessage.warning('请输入文本内容')
  }
  
  // 重置状态
  isTyping.value = false
  textInput.value = ''
}

// 橡皮擦功能
const erase = (x, y) => {
  const context = whiteboard.value.getContext('2d')
  const eraserSize = 20
  
  context.globalCompositeOperation = 'destination-out'
  context.beginPath()
  context.arc(x, y, eraserSize, 0, 2 * Math.PI)
  context.fill()
  context.globalCompositeOperation = 'source-over'
  
  console.log('橡皮擦使用，位置:', x, y)
  
  // 广播绘图操作到服务器
  broadcastDrawing('erase', {
    x, y,
    size: eraserSize
  })
}

// 广播绘图操作到服务器
const broadcastDrawing = (type, data) => {
  const socket = getSocket()
  if (socket) {
    socket.emit('whiteboard-draw', {
      userId: currentUser.value.id,
      userName: currentUser.value.name,
      type,
      data
    })
  }
}

// 接收绘图数据
const receiveDrawing = (drawingData) => {
  if (!whiteboard.value) return;
  
  const context = whiteboard.value.getContext('2d');
  const { type, data } = drawingData;
  
  // 设置通用绘图样式
  context.lineCap = 'round';
  context.lineJoin = 'round';
  
  switch (type) {
    case 'line':
      context.beginPath();
      context.moveTo(data.x1, data.y1);
      context.lineTo(data.x2, data.y2);
      context.strokeStyle = data.color;
      context.lineWidth = data.width;
      context.stroke();
      break;
    case 'pen':
      context.beginPath();
      context.moveTo(data.x1, data.y1);
      context.lineTo(data.x2, data.y2);
      context.strokeStyle = data.color;
      context.lineWidth = data.width;
      context.stroke();
      break;
    case 'rect':
      context.beginPath();
      context.rect(
        Math.min(data.x1, data.x2),
        Math.min(data.y1, data.y2),
        Math.abs(data.x2 - data.x1),
        Math.abs(data.y2 - data.y1)
      );
      context.strokeStyle = data.color;
      context.lineWidth = data.width;
      context.stroke();
      break;
    case 'circle':
      context.beginPath();
      context.arc(data.x, data.y, data.radius, 0, 2 * Math.PI);
      context.strokeStyle = data.color;
      context.lineWidth = data.width;
      context.stroke();
      break;
    case 'text':
      context.font = data.font;
      context.fillStyle = data.color;
      context.fillText(data.text, data.x, data.y);
      break;
    case 'erase':
      context.globalCompositeOperation = 'destination-out';
      context.beginPath();
      context.arc(data.x, data.y, data.size, 0, 2 * Math.PI);
      context.fill();
      context.globalCompositeOperation = 'source-over';
      break;
  }
}

// 初始化白板
const initWhiteboard = () => {
  if (whiteboard.value) {
    // 设置画布尺寸
    whiteboard.value.width = whiteboard.value.offsetWidth
    whiteboard.value.height = whiteboard.value.offsetHeight
    
    console.log('白板已初始化，尺寸:', whiteboard.value.width, 'x', whiteboard.value.height)
    
    // 设置画布样式
    const context = whiteboard.value.getContext('2d')
    context.lineCap = 'round'
    context.lineJoin = 'round'
    
    // 设置白板的CSS样式
    whiteboard.value.style.touchAction = 'none'; // 防止触摸滚动
    whiteboard.value.style.userSelect = 'none'; // 防止文本选择
    
    // 添加事件监听 - 绑定到canvas而不是document
    whiteboard.value.addEventListener('mousemove', handleMouseMove);
    whiteboard.value.addEventListener('mouseup', handleMouseUp);
    whiteboard.value.addEventListener('mouseleave', handleMouseUp); // 鼠标离开时停止绘制
    
    // 设置默认光标
    selectTool(currentTool.value);
    
    console.log('白板事件监听器已添加');
    ElMessage.success('绘画工具已准备就绪')
  } else {
    console.error('白板元素未找到');
    ElMessage.error('白板初始化失败');
  }
}

// 组件挂载
onMounted(async () => {
  // 初始化Socket连接
  initSocket({
    userId: currentUser.value.id,
    userName: currentUser.value.name,
    role: currentUser.value.role
  });
  
  const socket = getSocket();
  if (socket) {
    // 监听PPT页面变化
    socket.on('slide-changed', (data) => {
      // 如果自己没有控制权，则同步更新
      if (!hasControl.value) {
        currentSlide.value = data.slideIndex;
      }
    });
    
    // 监听白板绘图事件
    socket.on('whiteboard-draw', (data) => {
      // 如果不是自己发送的，则绘制
      if (data.userId !== currentUser.value.id) {
        receiveDrawing(data);
      }
    });
    
    // 监听新投票
    socket.on('new-vote', (voteData) => {
      // 检查是否已经在列表中
      const existingVoteIndex = activeVotes.value.findIndex(v => v.id === voteData.id);
      if (existingVoteIndex === -1) {
        activeVotes.value.push({
          ...voteData,
          hasVoted: false
        });

        // 显示通知
        ElMessage({
          message: `收到新投票: ${voteData.title}`,
          type: 'info',
          duration: 5000
        });
      }
    });

    // 监听教师回答
    socket.on('question-answered', (answerData) => {
      console.log('收到教师回答:', answerData);

      // 更新本地问题列表中对应问题的状态
      const questionIndex = questions.value.findIndex(q => q.id === answerData.questionId);
      if (questionIndex !== -1) {
        questions.value[questionIndex].answered = true;
        questions.value[questionIndex].answer = answerData.answer;
        questions.value[questionIndex].answeredAt = answerData.answeredAt;
        questions.value[questionIndex].answeredBy = answerData.answeredBy;
      }

      // 显示通知
      ElNotification({
        title: '教师已回答您的问题',
        message: `问题：${questions.value[questionIndex]?.content.substring(0, 30)}...\n回答：${answerData.answer.substring(0, 50)}...`,
        type: 'success',
        duration: 8000
      });
    });

    // 监听问题历史
    socket.on('classroom-questions-history', (data) => {
      console.log('收到问题历史:', data);
      if (data.questions && data.questions.length > 0) {
        // 合并历史问题，避免重复
        const existingIds = questions.value.map(q => q.id);
        const newQuestions = data.questions.filter(q => !existingIds.includes(q.id));
        questions.value = [...newQuestions, ...questions.value];
      }
    });

    // 监听问题提交确认
    socket.on('question-submitted', (data) => {
      if (data.success) {
        console.log('问题提交成功:', data.questionId);
      }
    });

    // 监听问题提交错误
    socket.on('question-error', (data) => {
      ElMessage.error(data.message);
    });
    
    // 监听投票状态更新
    socket.on('vote-updated', (voteResult) => {
      console.log('投票更新:', voteResult);
      
      // 查找对应的投票并更新状态
      const voteIndex = activeVotes.value.findIndex(v => v.id === voteResult.voteId);
      if (voteIndex !== -1) {
        // 如果是当前用户提交的投票，标记为已投票
        if (voteResult.userId === currentUser.value.id) {
          activeVotes.value[voteIndex].hasVoted = true;
        }
        
        // 更新投票的计数和结果
        if (!activeVotes.value[voteIndex].results) {
          activeVotes.value[voteIndex].results = [];
        }
        
        // 简单更新投票选项计数
        voteResult.optionIds.forEach(optionId => {
          const option = activeVotes.value[voteIndex].options[optionId];
          if (option) {
            if (!option.count) option.count = 0;
            option.count++;
          }
        });
      }
    });
    
    // 监听投票结束事件
    socket.on('vote-ended', (data) => {
      console.log('投票结束:', data);

      // 查找对应的投票并更新状态
      const voteIndex = activeVotes.value.findIndex(v => v.id === data.voteId);
      if (voteIndex !== -1) {
        activeVotes.value[voteIndex].status = 'ended';
        activeVotes.value[voteIndex].endedAt = data.endedAt;
      }
    });

    // 监听课堂加入成功事件
    socket.on('join-classroom-success', (data) => {
      console.log('加入课堂成功:', data);

      // 更新课堂状态
      classroomStatus.inClassroom = true;
      classroomStatus.classCode = data.classCode;
      classroomStatus.teacherName = data.teacherName;

      // 请求问题历史
      requestQuestionHistory(data.classCode);
    });
  }
  
  // 初始化PPT数据
  await initPPTData();
  
  // 加载投票数据
  await loadActiveVotes();
  
  // 加载问题数据
  await loadQuestions();
  
  // 初始化白板
  await nextTick(() => {
    initWhiteboard();
  });
  
  // 添加可见性变化监听
  document.addEventListener('visibilitychange', handleVisibilityChange);
});

// 组件卸载前
onBeforeUnmount(() => {
  // 移除可见性变化监听
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  
  // 移除鼠标事件监听
  if (whiteboard.value) {
    whiteboard.value.removeEventListener('mousemove', handleMouseMove);
    whiteboard.value.removeEventListener('mouseup', handleMouseUp);
    whiteboard.value.removeEventListener('mouseleave', handleMouseUp);
  }
  
  // 移除临时画布
  if (tempCanvas.value && tempCanvas.value.parentNode) {
    tempCanvas.value.parentNode.removeChild(tempCanvas.value);
  }
  
  // 移除Socket监听
  const socket = getSocket();
  if (socket) {
    socket.off('slide-changed');
    socket.off('whiteboard-draw');
    socket.off('new-vote');
    socket.off('vote-updated');
    socket.off('vote-ended');
  }
});
</script>

<template>
  <!-- 添加控制权相关UI -->
  <div class="student-view">
    <PageHeader title="学生课堂" />
    
    <!-- 课堂状态提示 -->
    <div class="classroom-status-panel">
      <div v-if="!classroomStatus.inClassroom" class="no-classroom-tip">
        <el-alert
          type="info"
          :closable="false"
          show-icon>
          <template #title>
            您当前未加入任何课堂
          </template>
          <template #default>
            请前往<router-link to="/dashboard/join-classroom" class="join-link">加入课堂</router-link>页面输入课堂码加入老师的课堂。
          </template>
        </el-alert>
      </div>
      
      <div v-else class="classroom-info">
        <el-alert
          type="success"
          :closable="false"
          show-icon>
          <div class="classroom-status">
            <strong>当前课堂：</strong> {{ classroomStatus.teacherName }}的课堂 (课堂码: {{ classroomStatus.classCode }})
            <el-button type="danger" size="small" plain @click="handleLeaveClassroom" style="margin-left: 10px">
              退出课堂
            </el-button>
          </div>
        </el-alert>
      </div>
    </div>
    
    <!-- 权限状态显示 -->
    <div class="permission-status-panel">
      <div v-if="hasControl" class="control-status">
        <el-alert
          title="您已获得控制权"
          type="success"
          description="现在您可以控制PPT翻页和使用绘图工具"
          show-icon
          :closable="false"
        />
      </div>
      
      <div v-else class="no-control-status">
        <el-alert
          title="权限状态"
          type="info"
          :closable="false"
          show-icon>
          <template #default>
            <div class="permission-info">
              <p>当前用户: <strong>{{ currentUser.name }}</strong></p>
              <p>控制权状态: 
                <el-tag :type="controlStatus.isControlGranted ? 'success' : 'info'">
                  {{ controlStatus.isControlGranted ? '已授权' : '未授权' }}
                </el-tag>
              </p>
              <p v-if="controlStatus.controllingUserName">
                当前控制者: <strong>{{ controlStatus.controllingUserName }}</strong>
              </p>
                             <div class="permission-actions">
                 <el-button @click="checkPermissionStatus" size="small" type="primary" plain>
                   检查权限详情
                 </el-button>
                 <el-button @click="refreshPermissionStatus" size="small" type="success" plain>
                   刷新权限状态
                 </el-button>
                 <el-button @click="forceGrantPermission" size="small" type="danger" plain>
                   临时授权 (调试)
                 </el-button>
               </div>
            </div>
          </template>
        </el-alert>
      </div>
    </div>
    
    <!-- 工具栏 (仅在有控制权时显示) -->
    <div v-if="hasControl" class="toolbar">
      <div class="toolbar-left">
        <span class="toolbar-label">工具:</span>
        <el-button-group>
          <el-button v-for="tool in tools" :key="tool.name" 
            :type="currentTool === tool.name ? 'primary' : ''" 
            size="small" 
            @click="selectTool(tool.name)"
            :title="tool.label">
            <el-icon><component :is="tool.icon"></component></el-icon>
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-center">
        <span class="toolbar-label">颜色:</span>
        <el-color-picker v-model="drawingColor" size="small" />
        
        <span class="toolbar-label">线宽:</span>
        <el-slider v-model="drawingWidth" :min="1" :max="10" :step="1" style="width: 80px;" />
      </div>
      
      <div class="toolbar-right">
                  <el-button @click="clearWhiteboard" size="small" type="danger" plain>
            清空白板
          </el-button>
          <el-button @click="testDrawing" size="small" type="info" plain>
            测试绘制
          </el-button>
          <el-button @click="checkPermissionStatus" size="small" type="warning" plain>
            检查权限
          </el-button>
          <el-button @click="refreshPermissionStatus" size="small" type="success" plain>
            刷新权限
          </el-button>
      </div>
    </div>
    
    <div class="main-content">
      <div class="slide-container">
        <div class="slide-controls" v-if="hasControl">
          <el-button @click="prevSlide" :disabled="currentSlide <= 1" type="primary" circle>
            <el-icon><arrow-left /></el-icon>
          </el-button>
          <span class="slide-indicator">{{ currentSlide }} / {{ totalSlides }}</span>
          <el-button @click="nextSlide" :disabled="currentSlide >= totalSlides" type="primary" circle>
            <el-icon><arrow-right /></el-icon>
          </el-button>
        </div>
        
        <div class="slide-content" 
          @touchstart="handleTouchStart" 
          @touchmove="handleTouchMove" 
          @touchend="handleTouchEnd">
          <div class="slide">
            <img v-if="pptInfo.images && pptInfo.images[currentSlide-1]" 
              :src="pptInfo.images[currentSlide-1]" 
              alt="幻灯片图片" />
            <div v-else class="slide-placeholder">
              <el-icon><Picture /></el-icon>
              <div>幻灯片 {{ currentSlide }}</div>
            </div>
          </div>
          
          <!-- 白板覆盖层 (仅在有控制权时显示) -->
          <canvas 
            v-if="hasControl"
            ref="whiteboard" 
            class="whiteboard"
            @mousedown="handleMouseDown"
          ></canvas>
          
          <!-- 文本输入弹出框 -->
          <div v-if="isTyping" class="text-input-overlay" :style="{left: startX + 'px', top: startY + 'px'}">
            <el-input 
              v-model="textInput" 
              placeholder="输入文本" 
              size="small"
              autofocus
              @keyup.enter="commitTextInput"
            >
              <template #append>
                <el-button @click="commitTextInput">确定</el-button>
              </template>
            </el-input>
          </div>
        </div>
      </div>
      
      <div class="interaction-panel">
        <div class="panel-header">
          <h3>课堂互动</h3>
        </div>
        
        <div class="action-buttons">
          <el-button @click="askQuestion" type="primary">
            <el-icon><ChatDotRound /></el-icon> 提问
          </el-button>
          
          <el-button @click="notesVisible = true" type="success">
            <el-icon><Edit /></el-icon> 记笔记
          </el-button>
          
          <el-button @click="annotationsVisible = true">
            <el-icon><Document /></el-icon> 查看笔记
          </el-button>
        </div>
        
        <!-- 活动投票显示 -->
        <div v-if="activeVotes.length > 0" class="active-votes">
          <div class="section-title">
            <h4>活动投票 ({{ activeVotes.length }})</h4>
          </div>
          
          <div class="vote-list">
            <el-card v-for="vote in activeVotes" :key="vote.id" class="vote-card">
              <template #header>
                <div class="vote-header">
                  <h5>{{ vote.title }}</h5>
                  <el-tag v-if="vote.hasVoted" type="success" size="small">已投票</el-tag>
                  <el-tag v-else type="warning" size="small">待投票</el-tag>
                </div>
              </template>
              
              <div class="vote-actions">
                <el-button 
                  v-if="!vote.hasVoted" 
                  @click="currentVote = vote; voteDialogVisible = true" 
                  type="primary" 
                  size="small">
                  参与投票
                </el-button>
                <el-button v-else size="small" type="info" disabled>已参与</el-button>
              </div>
            </el-card>
          </div>
        </div>
        
        <!-- 问题列表 -->
        <div v-if="questions.length > 0" class="questions-section">
          <div class="section-title">
            <h4>我的问题 ({{ questions.length }})</h4>
          </div>
          
          <div class="question-list">
            <el-card v-for="question in questions" :key="question.id" class="question-card">
              <div class="question-header">
                <div class="question-time">
                  {{ new Date(question.time).toLocaleTimeString() }}
                </div>
                <div class="question-status">
                  <el-tag v-if="question.answered" type="success" size="small">已回答</el-tag>
                  <el-tag v-else type="info" size="small">待回答</el-tag>
                </div>
              </div>

              <div class="question-content">
                {{ question.content }}
              </div>

              <!-- 教师回答显示 -->
              <div v-if="question.answered" class="teacher-answer">
                <div class="answer-label">
                  <el-icon><User /></el-icon>
                  教师回答：
                </div>
                <div class="answer-content">
                  {{ question.answer }}
                </div>
                <div class="answer-time">
                  回答时间：{{ new Date(question.answeredAt).toLocaleString() }}
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 投票对话框 -->
    <el-dialog
      v-model="voteDialogVisible"
      :title="currentVote?.title || '投票'"
      width="400px"
    >
      <template v-if="currentVote">
        <div class="vote-options">
          <div 
            v-for="(option, index) in currentVote.options" 
            :key="index"
            :class="['vote-option', {
              'selected': selectedOptions.includes(index)
            }]"
            @click="handleOptionSelect(index)"
          >
            <div class="option-selector">
              <el-checkbox 
                v-if="currentVote.isMultiple" 
                v-model="selectedOptions"
                :label="index"
              ></el-checkbox>
              <el-radio 
                v-else 
                v-model="selectedOptions[0]"
                :label="index"
              ></el-radio>
            </div>
            <div class="option-content">
              {{ option.text }}
            </div>
          </div>
        </div>
        
        <div class="vote-note" v-if="currentVote.isAnonymous">
          <el-alert
            title="这是一个匿名投票，您的选择不会被记录姓名"
            type="info"
            :closable="false"
          />
        </div>
      </template>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="voteDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitVoteAnswer">提交投票</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 问题对话框 -->
    <el-dialog
      v-model="questionsVisible"
      title="提交问题"
      width="400px"
    >
      <el-form>
        <el-form-item>
          <el-input
            v-model="newQuestion"
            type="textarea"
            rows="4"
            placeholder="请输入您的问题..."
          ></el-input>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="questionsVisible = false">取消</el-button>
          <el-button type="primary" @click="submitQuestion(newQuestion)">提交问题</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 笔记对话框 -->
    <el-drawer
      v-model="notesVisible"
      title="课堂笔记"
      direction="rtl"
      size="50%"
    >
      <div class="notes-editor">
        <el-form>
          <el-form-item label="笔记标题">
            <el-input v-model="noteTitle" placeholder="请输入笔记标题"></el-input>
          </el-form-item>
          
          <el-form-item label="笔记内容">
            <el-input
              v-model="noteContent"
              type="textarea"
              rows="10"
              placeholder="请输入笔记内容..."
            ></el-input>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary">保存笔记</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
    
    <!-- 课堂笔记面板 -->
    <el-drawer
      v-model="annotationsVisible"
      title="课堂笔记列表"
      direction="rtl"
      size="50%"
    >
      <SlideAnnotationsPanel 
        :slideId="currentSlide"
        :canEdit="true"
      />
    </el-drawer>
  </div>
</template>

<style scoped>
.student-view {
  padding: 0;
  height: 100%;
  overflow: hidden;
  background-color: #f5f7fa;
}

.classroom-status-panel {
  margin: 10px;
}

.join-link {
  color: var(--el-color-primary);
  font-weight: bold;
  text-decoration: none;
}

.join-link:hover {
  text-decoration: underline;
}

.classroom-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.permission-status-panel {
  margin: 10px;
}

.control-status {
  margin-bottom: 10px;
}

.no-control-status {
  margin-bottom: 10px;
}

.permission-info {
  line-height: 1.6;
}

.permission-info p {
  margin: 8px 0;
  color: #606266;
}

.permission-actions {
  margin-top: 15px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  margin: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  gap: 15px;
}

.toolbar-left {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
}

.toolbar-right {
  flex: 0 0 auto;
}

.toolbar-label {
  font-size: 14px;
  color: #606266;
  white-space: nowrap;
}

.toolbar-center .el-button-group .el-button.is-active,
.toolbar-center .el-button-group .el-button.is-active:focus,
.toolbar-center .el-button-group .el-button.is-active:hover {
  color: var(--el-button-hover-text-color);
  background-color: var(--el-button-hover-bg-color);
  border-color: var(--el-button-hover-border-color);
}

.whiteboard {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  cursor: crosshair;
}

.text-input-overlay {
  position: absolute;
  z-index: 20;
  min-width: 200px;
}

.main-content {
  display: flex;
  height: calc(100vh - 120px);
  overflow: hidden;
}

.slide-container {
  flex: 3;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  margin: 10px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.slide-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #ebeef5;
}

.slide-indicator {
  margin: 0 15px;
  font-size: 16px;
  font-weight: bold;
}

.slide-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.slide {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.slide img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.slide-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.slide-placeholder .el-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.interaction-panel {
  flex: 1;
  background-color: #fff;
  border-radius: 8px;
  margin: 10px;
  padding: 15px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.panel-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 10px;
}

.panel-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.section-title {
  margin: 15px 0 10px;
}

.section-title h4 {
  margin: 0;
  font-size: 16px;
  color: #606266;
}

.vote-list {
  margin-bottom: 20px;
}

.vote-card {
  margin-bottom: 10px;
}

.vote-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vote-header h5 {
  margin: 0;
  font-size: 14px;
}

.vote-actions {
  text-align: right;
}

.vote-options {
  margin-bottom: 20px;
}

.vote-option {
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.vote-option:hover {
  background-color: #f5f7fa;
}

.vote-option.selected {
  background-color: #ecf5ff;
  border-color: #409eff;
}

.option-selector {
  margin-right: 10px;
}

.option-content {
  flex: 1;
}

.vote-note {
  margin-top: 15px;
}

.question-list {
  margin-bottom: 20px;
}

.question-card {
  margin-bottom: 10px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #909399;
}

.question-content {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 12px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 6px;
}

.teacher-answer {
  background-color: #e8f5e8;
  padding: 12px;
  border-radius: 6px;
  border-left: 3px solid #67c23a;
  margin-top: 8px;
}

.answer-label {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #67c23a;
  font-weight: 500;
  margin-bottom: 6px;
}

.answer-content {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 6px;
  color: #333;
}

.answer-time {
  font-size: 12px;
  color: #909399;
  text-align: right;
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }
  
  .interaction-panel {
    height: 300px;
  }
}
</style> 