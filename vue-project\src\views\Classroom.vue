<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, computed, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { 
  getCurrentController, 
  transferControl, 
  recallControl, 
  changeSlide as changeSlideService, 
  getCurrentSlide, 
  getTotalSlides,
  getPPTInfo,
  updatePPTInfo,
  clearWhiteboardMarks,
  addWhiteboardMark,
  deleteWhiteboardMark,
  getSlideAnnotations,
  addSlideAnnotation
} from '../services/db/classroomService'
import StudentActivityMonitor from '../components/StudentActivityMonitor.vue'
import QuickResponsePanel from '../components/QuickResponsePanel.vue'
import SlideAnnotationsPanel from '../components/SlideAnnotationsPanel.vue'
// 引入Socket通信服务
import { 
  initSocket, 
  getSocket, 
  socketStatus, 
  controlStatus, 
  classroomStatus,
  grantControl, 
  revokeControl as socketRevokeControl,
  sendSlideChange,
  createVote as socketCreateVote,
  createClassroom,
  leaveClassroom,
  registerClassroomEventCallback
} from '../services/socketService'
// 引入文件上传相关逻辑
import { ElUpload } from 'element-plus';
import { Plus, Upload } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router'

// 当前用户信息
const currentUser = ref({
  userId: localStorage.getItem('userId') || '',
  userName: localStorage.getItem('userName') || '未知用户',
  role: localStorage.getItem('userRole') || '教师'
})

// 当前模式：教师模式或学生模式
const mode = ref('teacher')

// 当前控制权：teacher（教师）或 student（学生）
const currentControl = ref('teacher')

// 计算属性：当前在线学生列表
const onlineStudents = computed(() => {
  console.log('课堂状态:', classroomStatus.inClassroom, '学生列表:', classroomStatus.students);
  
  // 使用classroomStatus.students优先显示已加入课堂的学生
  if (classroomStatus.inClassroom) {
    // 确保直接返回课堂中的学生列表，不需要额外过滤
    return classroomStatus.students;
  } else {
    // 如果未创建课堂，则显示所有学生用户（过滤掉教师和自己）
    return socketStatus.users.filter(user => 
      user.role === '学生' && user.userId !== currentUser.value.userId
    );
  }
})

// 计算属性：是否有控制权
const hasControl = computed(() => {
  // 检查当前用户是否是教师或被授予了控制权
  if (currentUser.value.role === '教师') {
    return true;
  }
  
  // 检查是否被授予控制权，拥有完整访问权限
  return controlStatus.isControlGranted && controlStatus.controllingUserId === currentUser.value.userId;
});

// 计算属性：是否可以像教师一样操作（包括拥有控制权的学生）
const canOperateAsTeacher = computed(() => {
  // 教师或拥有完整操作权限的学生
  return currentUser.value.role === '教师' || 
         (controlStatus.isControlGranted && 
          controlStatus.controllingUserId === currentUser.value.userId && 
          controlStatus.hasFullAccess);
});

// 当前活动工具
const currentTool = ref('cursor')

// PPT信息对象
const pptInfo = ref({
  id: '',
  title: '示例课件',
  slideCount: 10,
  currentIndex: 1,
  images: []
})

// 侧边栏状态
const sidebarVisible = ref(true)
const activeSidePanel = ref('students') // students, quickResponse, annotations, questions

// 学生提问相关
const studentQuestions = ref([])
const newQuestionNotification = ref(0)

// 所有可用的工具
const tools = [
  { name: 'cursor', icon: 'Pointer', label: '光标' },
  { name: 'pen', icon: 'EditPen', label: '笔' },
  { name: 'line', icon: 'Minus', label: '直线' },
  { name: 'rect', icon: 'Rectangle', label: '矩形' },
  { name: 'circle', icon: 'ForkSpoon', label: '圆形' },
  { name: 'text', icon: 'Document', label: '文本' },
  { name: 'eraser', icon: 'DeleteFilled', label: '橡皮擦' }
]

// 选择工具
const selectTool = (tool) => {
  currentTool.value = tool
}

// 通过控制权给学生
const transferControlToStudent = (studentId, studentName) => {
  if (currentUser.value.role !== '教师') {
    ElMessage.warning('只有教师才能转移控制权');
    return;
  }
  
  ElMessageBox.confirm(
    `确定要将控制权转移给学生 ${studentName || studentId} 吗？`,
    '转移控制权',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 调用Socket服务转移控制权
    grantControl(studentId);
    currentControl.value = 'student';
    ElMessage.success(`已将控制权转移给 ${studentName || studentId}`);
  }).catch(() => {});
}

// 收回控制权
const retrieveControl = () => {
  if (currentUser.value.role !== '教师') {
    ElMessage.warning('只有教师才能收回控制权');
    return;
  }
  
  // 调用Socket服务收回控制权
  socketRevokeControl();
  currentControl.value = 'teacher';
  ElMessage.success('已收回控制权');
}

// 当前幻灯片
const currentSlide = ref(1)
// 总幻灯片数
const totalSlides = ref(10)

// 上一张幻灯片
const prevSlide = () => {
  // 检查是否有控制权
  if (!hasControl.value) {
    ElMessage.warning('您当前没有控制权');
    return;
  }
  
  if (currentSlide.value > 1) {
    currentSlide.value--;
    
    // 通过Socket服务广播幻灯片变化
    sendSlideChange({
      slideIndex: currentSlide.value,
      totalSlides: totalSlides.value
    });
    
    // 更新本地数据库状态
    changeSlideService(currentUser.value.userId, currentSlide.value)
      .catch(error => {
        console.error('更新幻灯片失败', error);
      });
  }
}

// 下一张幻灯片
const nextSlide = () => {
  // 检查是否有控制权
  if (!hasControl.value) {
    ElMessage.warning('您当前没有控制权');
    return;
  }
  
  if (currentSlide.value < totalSlides.value) {
    currentSlide.value++;
    
    // 通过Socket服务广播幻灯片变化
    sendSlideChange({
      slideIndex: currentSlide.value,
      totalSlides: totalSlides.value
    });
    
    // 更新本地数据库状态
    changeSlideService(currentUser.value.userId, currentSlide.value)
      .catch(error => {
        console.error('更新幻灯片失败', error);
      });
  }
}

// 白板相关变量
const whiteboard = ref(null)
const isDrawing = ref(false)
const lastX = ref(0)
const lastY = ref(0)
const startX = ref(0) // 绘制起点X
const startY = ref(0) // 绘制起点Y
const tempCanvas = ref(null) // 临时画布用于预览
const textInput = ref('') // 文本输入
const isTyping = ref(false) // 是否在输入文本
const drawingColor = ref('#f56c6c') // 绘图颜色
const drawingWidth = ref(2) // 绘图线宽

// 是否正在创建投票
const isCreatingVote = ref(false)

// 投票表单
const voteForm = reactive({
  title: '',
  options: ['', ''],
  isMultiple: false,
  isAnonymous: false
})

// 添加选项
const addOption = () => {
  voteForm.options.push('')
}

// 移除选项
const removeOption = (index) => {
  if (voteForm.options.length > 2) {
    voteForm.options.splice(index, 1)
  } else {
    ElMessage.warning('至少需要2个选项')
  }
  }
  
// 打开投票创建器
const openVoteCreator = () => {
  isCreatingVote.value = true
  
  // 重置表单
  voteForm.title = ''
  voteForm.options = ['', '']
  voteForm.isMultiple = false
  voteForm.isAnonymous = false
}

// 创建投票
const createVote = () => {
  // 表单验证
  if (!voteForm.title.trim()) {
    ElMessage.warning('请输入投票标题');
    return;
  }
  
  if (voteForm.options.some(opt => !opt.trim())) {
    ElMessage.warning('选项内容不能为空');
    return;
  }
  
  // 创建投票数据对象
  const voteData = {
    title: voteForm.title,
    options: voteForm.options.map(text => ({ text, count: 0 })),
    isMultiple: voteForm.isMultiple,
    isAnonymous: voteForm.isAnonymous,
    status: 'active',
    createdBy: currentUser.value.userId,
    createdAt: new Date().toISOString()
  };
  
  // 通过Socket广播投票
  socketCreateVote(voteData);
  
  ElMessage.success('投票创建成功');
  isCreatingVote.value = false;
}
    
    // 清空白板
const clearWhiteboard = () => {
  if (!whiteboard.value) return;
  
  const context = whiteboard.value.getContext('2d')
  context.clearRect(0, 0, whiteboard.value.width, whiteboard.value.height)
  
  // 如果有临时画布，也清空它
  if (tempCanvas.value) {
    const tempContext = tempCanvas.value.getContext('2d')
    tempContext.clearRect(0, 0, tempCanvas.value.width, tempCanvas.value.height)
  }
  
  // 实际项目应调用后端API
  clearWhiteboardMarks(currentUser.value.role === '教师' ? 'teacher1' : 'student1')
    .catch(error => {
      ElMessage.error('清空白板失败')
      console.error(error)
    })
}

// 创建临时画布
const createTempCanvas = () => {
  if (!tempCanvas.value) {
    tempCanvas.value = document.createElement('canvas')
    tempCanvas.value.width = whiteboard.value.width
    tempCanvas.value.height = whiteboard.value.height
    tempCanvas.value.style.position = 'absolute'
    tempCanvas.value.style.top = '0'
    tempCanvas.value.style.left = '0'
    tempCanvas.value.style.pointerEvents = 'none'
    whiteboard.value.parentNode.appendChild(tempCanvas.value)
  }
}

// 鼠标按下事件
const handleMouseDown = (e) => {
  // 检查是否有控制权
  if (!hasControl.value) {
    ElMessage.warning('您没有控制权限');
    return;
  }
  
  // 处理文本工具的特殊情况
  if (currentTool.value === 'text') {
    isTyping.value = true;
    startX.value = e.offsetX;
    startY.value = e.offsetY;
    return;
  }
  
  // 其他绘图逻辑
  isDrawing.value = true;
  lastX.value = e.offsetX;
  lastY.value = e.offsetY;
  startX.value = e.offsetX;
  startY.value = e.offsetY;
  
  // 使用临时画布预览
  if (currentTool.value !== 'pen' && currentTool.value !== 'cursor' && currentTool.value !== 'eraser') {
    createTempCanvas();
  }
};

// 鼠标移动事件
const handleMouseMove = (e) => {
  if (!isDrawing.value) return
  
  const rect = whiteboard.value.getBoundingClientRect()
  const x = e.clientX - rect.left
  const y = e.clientY - rect.top
  
  if (currentTool.value === 'cursor') {
    // 光标模式下不绘制
    return
  } else if (currentTool.value === 'pen') {
    // 自由绘制模式，直接在主画布上绘制
    drawLine(lastX.value, lastY.value, x, y)
  } else if (currentTool.value === 'eraser') {
    // 橡皮擦模式
    erase(x, y)
  } else {
    // 其他工具使用临时画布进行预览
    const tempContext = tempCanvas.value.getContext('2d')
    tempContext.clearRect(0, 0, tempCanvas.value.width, tempCanvas.value.height)
    
    switch (currentTool.value) {
      case 'line':
        drawLinePreview(startX.value, startY.value, x, y)
        break
      case 'rect':
        drawRectPreview(startX.value, startY.value, x, y)
        break
      case 'circle':
        drawCirclePreview(startX.value, startY.value, x, y)
        break
    }
  }
  
  lastX.value = x
  lastY.value = y
}

// 鼠标释放事件
const handleMouseUp = (e) => {
  if (!isDrawing.value) return
  
  const rect = whiteboard.value.getBoundingClientRect()
  const x = e.clientX - rect.left
  const y = e.clientY - rect.top
  
  if (currentTool.value !== 'pen' && currentTool.value !== 'cursor' && currentTool.value !== 'eraser') {
    // 从临时画布复制到主画布
  const context = whiteboard.value.getContext('2d')
  
    switch (currentTool.value) {
      case 'line':
        drawLine(startX.value, startY.value, x, y)
        break
      case 'rect':
        drawRect(startX.value, startY.value, x, y)
        break
      case 'circle':
        drawCircle(startX.value, startY.value, x, y)
        break
    }
    
    // 清空临时画布
    const tempContext = tempCanvas.value.getContext('2d')
    tempContext.clearRect(0, 0, tempCanvas.value.width, tempCanvas.value.height)
  }
  
  isDrawing.value = false
}

// 绘制直线
const drawLine = (x1, y1, x2, y2) => {
  const context = whiteboard.value.getContext('2d')
    context.beginPath()
  context.moveTo(x1, y1)
  context.lineTo(x2, y2)
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
    context.stroke()
    
  // 记录到服务器
  broadcastDrawing('line', {
    x1, y1, x2, y2,
    color: drawingColor.value,
    width: drawingWidth.value
  })
}

// 预览直线
const drawLinePreview = (x1, y1, x2, y2) => {
  const context = tempCanvas.value.getContext('2d')
  context.beginPath()
  context.moveTo(x1, y1)
  context.lineTo(x2, y2)
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.stroke()
}

// 绘制矩形
const drawRect = (x1, y1, x2, y2) => {
  const context = whiteboard.value.getContext('2d')
  context.beginPath()
  context.rect(
    Math.min(x1, x2),
    Math.min(y1, y2),
    Math.abs(x2 - x1),
    Math.abs(y2 - y1)
  )
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.stroke()
  
  // 记录到服务器
  broadcastDrawing('rect', {
    x1, y1, x2, y2,
    color: drawingColor.value,
    width: drawingWidth.value
  })
}

// 预览矩形
const drawRectPreview = (x1, y1, x2, y2) => {
  const context = tempCanvas.value.getContext('2d')
  context.beginPath()
  context.rect(
    Math.min(x1, x2),
    Math.min(y1, y2),
    Math.abs(x2 - x1),
    Math.abs(y2 - y1)
  )
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.stroke()
}

// 绘制圆形
const drawCircle = (x1, y1, x2, y2) => {
  const context = whiteboard.value.getContext('2d')
  const radius = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2))
  
  context.beginPath()
  context.arc(x1, y1, radius, 0, 2 * Math.PI)
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.stroke()
  
  // 记录到服务器
  broadcastDrawing('circle', {
    x: x1, 
    y: y1, 
    radius,
    color: drawingColor.value,
    width: drawingWidth.value
  })
}

// 预览圆形
const drawCirclePreview = (x1, y1, x2, y2) => {
  const context = tempCanvas.value.getContext('2d')
  const radius = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2))
  
  context.beginPath()
  context.arc(x1, y1, radius, 0, 2 * Math.PI)
  context.strokeStyle = drawingColor.value
  context.lineWidth = drawingWidth.value
  context.stroke()
}

// 提交文本输入
const commitTextInput = () => {
  if (isTyping.value && textInput.value.trim()) {
    const context = whiteboard.value.getContext('2d')
    context.font = '16px Arial'
    context.fillStyle = drawingColor.value
    context.fillText(textInput.value, startX.value, startY.value)
    
    // 记录到服务器
    broadcastDrawing('text', {
      x: startX.value,
      y: startY.value,
      text: textInput.value,
      color: drawingColor.value,
      font: '16px Arial'
    })
  }
  
  // 重置状态
  isTyping.value = false
  textInput.value = ''
}

// 橡皮擦功能
const erase = (x, y) => {
  const context = whiteboard.value.getContext('2d')
  const eraserSize = 20
  
  context.globalCompositeOperation = 'destination-out'
  context.beginPath()
  context.arc(x, y, eraserSize, 0, 2 * Math.PI)
  context.fill()
  context.globalCompositeOperation = 'source-over'
  
  // 记录到服务器
  broadcastDrawing('erase', {
    x, y,
    size: eraserSize
  })
}

// 广播绘图操作到服务器
const broadcastDrawing = (type, data) => {
  const socket = getSocket()
  if (socket) {
    socket.emit('whiteboard-draw', {
      userId: currentUser.value.userId,
      userName: currentUser.value.userName,
      type,
      data
    })
  }
}

// 触摸开始事件
const handleTouchStart = (e) => {
  // 检查是否有控制权
  if (!hasControl.value) {
    ElMessage.warning('您没有控制权限');
    return;
  }
  
  e.preventDefault(); // 阻止默认行为
  
  if (currentTool.value === 'text' && !isTyping.value) {
    // 处理文本工具
    const rect = whiteboard.value.getBoundingClientRect();
    const touch = e.touches[0];
    startX.value = touch.clientX - rect.left;
    startY.value = touch.clientY - rect.top;
    
    // 显示文本输入框
    isTyping.value = true;
    textInput.value = '';
    
    // 阻止继续处理
    return;
  }
  
  if (isTyping.value) {
    // 如果正在输入文本，先完成文本输入
    commitTextInput();
    return;
  }
  
  isDrawing.value = true;
  const touch = e.touches[0];
  const rect = whiteboard.value.getBoundingClientRect();
  startX.value = touch.clientX - rect.left;
  startY.value = touch.clientY - rect.top;
  lastX.value = startX.value;
  lastY.value = startY.value;
  
  if (currentTool.value !== 'pen' && currentTool.value !== 'cursor' && currentTool.value !== 'eraser') {
    // 创建临时画布用于预览
    createTempCanvas();
  }
  
  if (currentTool.value === 'eraser') {
    // 如果是橡皮擦，直接开始擦除
    erase(lastX.value, lastY.value);
  }
};

// 触摸移动事件
const handleTouchMove = (e) => {
  if (!isDrawing.value) return
  
  e.preventDefault() // 阻止默认行为
  
  const touch = e.touches[0]
  const rect = whiteboard.value.getBoundingClientRect()
  const x = touch.clientX - rect.left
  const y = touch.clientY - rect.top
  
  if (currentTool.value === 'cursor') {
    // 光标模式下不绘制
    return
  } else if (currentTool.value === 'pen') {
    // 自由绘制模式，直接在主画布上绘制
    drawLine(lastX.value, lastY.value, x, y)
  } else if (currentTool.value === 'eraser') {
    // 橡皮擦模式
    erase(x, y)
  } else {
    // 其他工具使用临时画布进行预览
    const tempContext = tempCanvas.value.getContext('2d')
    tempContext.clearRect(0, 0, tempCanvas.value.width, tempCanvas.value.height)
    
    switch (currentTool.value) {
      case 'line':
        drawLinePreview(startX.value, startY.value, x, y)
        break
      case 'rect':
        drawRectPreview(startX.value, startY.value, x, y)
        break
      case 'circle':
        drawCirclePreview(startX.value, startY.value, x, y)
        break
    }
  }
  
  lastX.value = x
  lastY.value = y
}

// 触摸结束事件
const handleTouchEnd = (e) => {
  if (!isDrawing.value) return
  
  e.preventDefault() // 阻止默认行为
  
  if (tempCanvas.value && currentTool.value !== 'pen' && currentTool.value !== 'cursor' && currentTool.value !== 'eraser') {
    // 从临时画布复制到主画布
    const context = whiteboard.value.getContext('2d')
    
    switch (currentTool.value) {
      case 'line':
        drawLine(startX.value, startY.value, lastX.value, lastY.value)
        break
      case 'rect':
        drawRect(startX.value, startY.value, lastX.value, lastY.value)
        break
      case 'circle':
        drawCircle(startX.value, startY.value, lastX.value, lastY.value)
        break
    }
    
    // 清空临时画布
    const tempContext = tempCanvas.value.getContext('2d')
    tempContext.clearRect(0, 0, tempCanvas.value.width, tempCanvas.value.height)
  }
  
  isDrawing.value = false
}

// 选择学生
const handleSelectStudent = (student) => {
  ElMessage.info(`已选择学生: ${student.name}`)
  // 实际项目中可以显示学生详情或发送消息
}

// 加载PPT数据
const loadPPTData = async () => {
  try {
    // 获取当前页码
    currentSlide.value = await getCurrentSlide()
    // 获取总页数
    totalSlides.value = await getTotalSlides()
    // 获取PPT详细信息
    const pptData = await getPPTInfo()
    pptInfo.value = pptData
  } catch (error) {
    console.error('加载PPT数据失败:', error)
    ElMessage.error('加载PPT数据失败')
  }
}

// 组件挂载后的生命周期钩子
onMounted(async () => {
  try {
    // 初始化Socket连接
    const socket = initSocket({
      userId: currentUser.value.userId,
      userName: currentUser.value.userName,
      role: currentUser.value.role
    });
    
    // 注册课堂创建事件回调
    registerClassroomEventCallback('onClassroomCreated', (data) => {
      console.log('课堂创建回调被触发:', data);
      // 关闭课堂管理面板
      showClassroomPanel.value = false;
      // 显示成功通知
      ElNotification({
        title: '课堂创建成功',
        message: `课堂码: ${data.classCode}，请分享给学生`,
        type: 'success',
        duration: 0
      });
    });
    
    // 监听幻灯片变更事件
    socket.on('slide-changed', (data) => {
      // 如果自己没有控制权，则跟随变更
      if (!hasControl.value) {
        currentSlide.value = data.slideIndex;
      }
    });
    
    // 监听特别授权通知
    socket.on('control-granted-to-you', (data) => {
      ElMessage.success(data.message);
      // 显示功能提示
      ElNotification({
        title: '控制权已获得',
        message: '您现在可以：\n1. 切换PPT页面\n2. 使用绘图工具\n3. 发起投票',
        type: 'success',
        duration: 5000,
        position: 'bottom-right'
      });
    });
    
    // 监听白板绘图事件
    socket.on('whiteboard-draw', (data) => {
      // 如果不是自己发送的，则绘制
      if (data.userId !== currentUser.value.userId) {
        receiveDrawing(data);
      }
    });

    // 监听学生提问（仅教师端）
    if (currentUser.value.role === '教师') {
      socket.on('new-question', (questionData) => {
        console.log('收到学生提问:', questionData);
        handleNewQuestion(questionData);
      });

      // 监听问题历史
      socket.on('classroom-questions-history', (data) => {
        console.log('收到问题历史:', data);
        if (data.questions && data.questions.length > 0) {
          // 合并历史问题，避免重复
          const existingIds = studentQuestions.value.map(q => q.id);
          const newQuestions = data.questions.filter(q => !existingIds.includes(q.id));
          studentQuestions.value = [...newQuestions, ...studentQuestions.value];
        }
      });

      // 请求当前课堂的问题历史
      if (classroomInfo.value.classCode) {
        requestQuestionHistory(classroomInfo.value.classCode);
      }
    }
    
    // 初始化白板
    if (whiteboard.value) {
      whiteboard.value.width = whiteboard.value.offsetWidth;
      whiteboard.value.height = whiteboard.value.offsetHeight;
      
      // 添加鼠标事件监听
      whiteboard.value.addEventListener('mousedown', handleMouseDown);
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      
      // 添加触摸事件监听
      whiteboard.value.addEventListener('touchstart', handleTouchStart, { passive: false });
      whiteboard.value.addEventListener('touchmove', handleTouchMove, { passive: false });
      whiteboard.value.addEventListener('touchend', handleTouchEnd, { passive: false });
    }
    
    // 获取初始数据
    const slideIndex = await getCurrentSlide();
    currentSlide.value = slideIndex;
    
    const slideCount = await getTotalSlides();
    totalSlides.value = slideCount;
    
    const pptData = await getPPTInfo();
    if (pptData) {
      pptInfo.value = pptData;
    }
  } catch (error) {
    console.error('初始化数据失败', error);
    ElMessage.error('加载数据失败');
  }
});

// 接收绘图数据
const receiveDrawing = (drawData) => {
  if (!whiteboard.value) return;
  
  const { type, data } = drawData;
  const context = whiteboard.value.getContext('2d');
  
  switch (type) {
    case 'line':
      context.beginPath();
      context.moveTo(data.x1, data.y1);
      context.lineTo(data.x2, data.y2);
      context.strokeStyle = data.color;
      context.lineWidth = data.width;
      context.stroke();
      break;
      
    case 'rect':
      context.beginPath();
      context.rect(
        Math.min(data.x1, data.x2),
        Math.min(data.y1, data.y2),
        Math.abs(data.x2 - data.x1),
        Math.abs(data.y2 - data.y1)
      );
      context.strokeStyle = data.color;
      context.lineWidth = data.width;
      context.stroke();
      break;
      
    case 'circle':
      context.beginPath();
      context.arc(data.x, data.y, data.radius, 0, 2 * Math.PI);
      context.strokeStyle = data.color;
      context.lineWidth = data.width;
      context.stroke();
      break;
      
    case 'text':
      context.font = data.font;
      context.fillStyle = data.color;
      context.fillText(data.text, data.x, data.y);
      break;
      
    case 'erase':
      context.globalCompositeOperation = 'destination-out';
      context.beginPath();
      context.arc(data.x, data.y, data.size, 0, 2 * Math.PI);
      context.fill();
      context.globalCompositeOperation = 'source-over';
      break;
  }
}

// 组件卸载前的生命周期钩子
onBeforeUnmount(() => {
  // 获取Socket实例
  const socket = getSocket();
  if (socket) {
    // 移除事件监听
    socket.off('slide-changed');
    socket.off('whiteboard-draw');
  }
  
  // 移除鼠标事件监听
  if (whiteboard.value) {
    whiteboard.value.removeEventListener('mousedown', handleMouseDown);
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
    
    // 移除触摸事件监听
    whiteboard.value.removeEventListener('touchstart', handleTouchStart);
    whiteboard.value.removeEventListener('touchmove', handleTouchMove);
    whiteboard.value.removeEventListener('touchend', handleTouchEnd);
  }
  
  // 移除临时画布
  if (tempCanvas.value && tempCanvas.value.parentNode) {
    tempCanvas.value.parentNode.removeChild(tempCanvas.value);
  }
});

// 窗口大小变化时重新调整白板尺寸
window.addEventListener('resize', () => {
  if (whiteboard.value) {
    whiteboard.value.width = whiteboard.value.offsetWidth
    whiteboard.value.height = whiteboard.value.offsetHeight
  }
})

// 在模板部分加入学生列表组件
const toggleSidebar = () => {
  sidebarVisible.value = !sidebarVisible.value;
}

const switchSidePanel = (panel) => {
  activeSidePanel.value = panel;

  // 如果切换到问题面板，清除通知计数
  if (panel === 'questions') {
    newQuestionNotification.value = 0;
  }
}

// 处理新的学生提问
const handleNewQuestion = (questionData) => {
  // 添加到问题列表
  studentQuestions.value.unshift({
    ...questionData,
    id: Date.now() + Math.random().toString(36).substr(2, 9),
    receivedAt: new Date().toISOString()
  });

  // 增加通知计数
  newQuestionNotification.value++;

  // 显示通知
  ElNotification({
    title: '收到新提问',
    message: `学生 ${questionData.userName} 提出了问题：${questionData.content.substring(0, 30)}...`,
    type: 'info',
    duration: 5000,
    onClick: () => {
      switchSidePanel('questions');
    }
  });
}

// 回答学生问题
const answerQuestion = (question, answer) => {
  if (!answer.trim()) {
    ElMessage.warning('回答内容不能为空');
    return;
  }

  // 更新问题状态
  const questionIndex = studentQuestions.value.findIndex(q => q.id === question.id);
  if (questionIndex !== -1) {
    studentQuestions.value[questionIndex].answered = true;
    studentQuestions.value[questionIndex].answer = answer;
    studentQuestions.value[questionIndex].answeredAt = new Date().toISOString();
    studentQuestions.value[questionIndex].answeredBy = currentUser.value.userName;
  }

  // 通过Socket发送回答给学生
  const socket = getSocket();
  if (socket) {
    socket.emit('answer-question', {
      questionId: question.id,
      answer: answer,
      answeredBy: currentUser.value.userName,
      answeredAt: new Date().toISOString(),
      studentId: question.userId
    });
  }

  ElMessage.success('问题回答已发送');
}

// 请求课堂问题历史
const requestQuestionHistory = (classCode) => {
  const socket = getSocket();
  if (socket) {
    socket.emit('get-classroom-questions', { classCode });
  }
}

// 课堂管理相关
const showClassroomPanel = ref(false)
const isCreatingClassroom = ref(false)

// 显示课堂管理面板
const openClassroomPanel = () => {
  showClassroomPanel.value = true
}

// 创建新课堂
const handleCreateClassroom = async () => {
  console.log('创建课堂按钮被点击');
  
  // 设置创建状态
  isCreatingClassroom.value = true;
  
  // 首先检查socket连接
  const socket = getSocket();
  if (!socket) {
    console.error('Socket未连接，尝试重新初始化');
    
    // 尝试重新初始化连接
    initSocket({
      userId: currentUser.value.userId,
      userName: currentUser.value.userName,
      role: currentUser.value.role
    });
    
    // 再次检查
    if (!getSocket()) {
      ElMessage.error('无法连接通信服务器，请确保服务器已启动');
      isCreatingClassroom.value = false;
      return;
    }
  }
  
  try {
  if (classroomStatus.inClassroom) {
      const result = await ElMessageBox.confirm(
      '您已在一个课堂中，创建新课堂将退出当前课堂。是否继续？',
      '创建新课堂',
      {
        confirmButtonText: '继续',
        cancelButtonText: '取消',
        type: 'warning'
      }
      );
      
      console.log('确认创建新课堂，先退出当前课堂');
      // 先退出当前课堂
      leaveClassroom();
      
      // 等待一小段时间确保退出课堂完成
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    console.log('调用createClassroom()函数');
        ElMessage.info('正在创建课堂...');
    
    // 调用创建课堂函数并等待结果
    const response = await createClassroom();
    console.log('创建课堂成功，收到响应:', response);
    
    // 确保课堂码已经更新
    if (response && response.classCode) {
      // 更新课堂码状态
      if (!classroomStatus.inClassroom || classroomStatus.classCode !== response.classCode) {
        ElMessage.success(`课堂创建成功！课堂码: ${response.classCode}`);
        
        // 显示课堂码弹窗
        ElNotification({
          title: '课堂创建成功',
          message: `课堂码: ${response.classCode}，请分享给学生`,
          type: 'success',
          duration: 0
        });
      }
  } else {
      throw new Error('创建课堂失败：没有收到有效的课堂码');
    }
  } catch (error) {
    console.error('创建课堂失败:', error);
    ElMessage.error(`创建课堂失败: ${error.message || '未知错误'}`);
  } finally {
    // 关闭面板和重置创建状态
    showClassroomPanel.value = false;
    isCreatingClassroom.value = false;
  }
}

// 结束当前课堂
const handleEndClassroom = () => {
  ElMessageBox.confirm(
    '确定要结束当前课堂吗？所有学生将被断开连接。',
    '结束课堂',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    leaveClassroom();
  }).catch(() => {});
}

// 添加缺失的函数
const openControlTransferDialog = () => {
  // 如果没有在线学生，提示用户
  if (onlineStudents.value.length === 0) {
    ElMessage.warning('暂无在线学生，无法转移控制权');
    return;
  }
  
  // 这里可以实现显示一个对话框供教师选择学生
  // 简化处理：直接提示用户点击学生列表中的授权按钮
  ElMessage.info('请在右侧学生列表中点击"授权"按钮，将控制权授予特定学生');
}

// PPT上传相关
const uploadPPTDialogVisible = ref(false);
const pptUploadProgress = ref(0);
const isUploading = ref(false);
const uploadedFiles = ref([]);
const uploadAction = ref('/api/upload-ppt'); // 实际项目中应替换为实际的上传API地址

// 打开上传PPT对话框
const openUploadPPTDialog = () => {
  if (currentUser.value.role !== '教师') {
    ElMessage.warning('只有教师才能上传PPT');
    return;
  }
  
  uploadPPTDialogVisible.value = true;
  uploadedFiles.value = []; // 重置上传文件列表
};

// 真实PPT上传成功处理
const handlePPTUploadSuccess = async (response, file) => {
  isUploading.value = false;
  pptUploadProgress.value = 100;

  try {
    // 读取PPT文件并转换为图片
    const images = await convertPPTToImages(file);

    // 更新PPT信息
    pptInfo.value = {
      ...pptInfo.value,
      title: file.name.replace('.pptx', '').replace('.ppt', ''),
      slideCount: images.length,
      images: images,
      fileName: file.name,
      fileSize: file.size,
      uploadTime: new Date().toISOString()
    };

    // 重置当前幻灯片
    currentSlide.value = 1;
    totalSlides.value = images.length;

    // 更新本地数据库状态
    await updatePPTInfo(pptInfo.value);

    ElMessage.success('PPT上传并转换成功');
    uploadPPTDialogVisible.value = false;

    // 通过Socket广播PPT更新给所有学生
    const socket = getSocket();
    if (socket) {
      socket.emit('ppt-updated', {
        pptInfo: pptInfo.value,
        currentSlide: currentSlide.value,
        totalSlides: totalSlides.value
      });
    }
  } catch (error) {
    console.error('PPT处理失败:', error);
    ElMessage.error('PPT处理失败: ' + error.message);
    isUploading.value = false;
  }
};

// 将PPT文件转换为图片数组
const convertPPTToImages = async (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = async (e) => {
      try {
        // 创建一个临时的文件URL
        const fileUrl = URL.createObjectURL(file);

        // 由于浏览器限制，我们无法直接解析PPT文件
        // 这里我们创建一个占位符图片数组，实际项目中应该调用后端API
        const images = [];
        const slideCount = Math.floor(Math.random() * 15) + 5; // 5-20张幻灯片

        for (let i = 1; i <= slideCount; i++) {
          // 创建一个canvas来生成占位符图片
          const canvas = document.createElement('canvas');
          canvas.width = 800;
          canvas.height = 600;
          const ctx = canvas.getContext('2d');

          // 绘制占位符内容
          ctx.fillStyle = '#f0f0f0';
          ctx.fillRect(0, 0, 800, 600);

          ctx.fillStyle = '#333';
          ctx.font = '48px Arial';
          ctx.textAlign = 'center';
          ctx.fillText(`幻灯片 ${i}`, 400, 280);

          ctx.font = '24px Arial';
          ctx.fillText(`来自: ${file.name}`, 400, 320);

          ctx.font = '16px Arial';
          ctx.fillText(`上传时间: ${new Date().toLocaleString()}`, 400, 350);

          // 转换为图片URL
          const imageUrl = canvas.toDataURL('image/png');
          images.push(imageUrl);
        }

        // 清理临时URL
        URL.revokeObjectURL(fileUrl);

        resolve(images);
      } catch (error) {
        reject(error);
      }
    };

    reader.onerror = () => {
      reject(new Error('文件读取失败'));
    };

    reader.readAsArrayBuffer(file);
  });
};

// 处理PPT上传进度
const handlePPTUploadProgress = (event) => {
  isUploading.value = true;
  pptUploadProgress.value = Math.round(event.percent);
};

// 模拟上传处理（实际项目中应替换为真实上传逻辑）
const handlePPTUpload = (file) => {
  // 添加到上传文件列表
  uploadedFiles.value.push(file);
  
  // 验证文件类型
  const isValidType = file.type === 'application/vnd.ms-powerpoint' || 
                     file.type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation';
  
  if (!isValidType) {
    ElMessage.error('只支持PPT或PPTX文件上传');
    return false;
  }
  
  // 模拟上传进度
  let progress = 0;
  isUploading.value = true;
  
  const progressInterval = setInterval(() => {
    progress += 5;
    pptUploadProgress.value = progress;
    
    if (progress >= 100) {
      clearInterval(progressInterval);
      // 模拟上传完成后调用成功回调
      setTimeout(() => {
        handlePPTUploadSuccess({ success: true }, file);
      }, 500);
    }
  }, 200);
  
  // 阻止实际上传，使用自定义处理
  return false;
};
</script>

<template>
  <div class="classroom-container">
    <!-- 主界面 -->
    <div class="main-content">
      <!-- 顶部工具栏 -->
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button @click="toggleSidebar" size="small" :icon="sidebarVisible ? 'Menu' : 'More'">
            {{ sidebarVisible ? '收起侧边栏' : '展开侧边栏' }}
          </el-button>
          
          <!-- 添加课堂管理按钮 -->
          <el-button 
            v-if="canOperateAsTeacher"
            @click="openClassroomPanel" 
            type="primary" 
            size="small">
            {{ classroomStatus.inClassroom ? '课堂管理' : '创建课堂' }}
          </el-button>
          
          <!-- 添加PPT上传按钮 -->
          <el-button 
            v-if="canOperateAsTeacher"
            @click="openUploadPPTDialog" 
            type="success" 
            size="small">
            <el-icon><Upload /></el-icon> 上传PPT
          </el-button>
        </div>
        
        <!-- 课堂状态展示 -->
        <div v-if="classroomStatus.inClassroom" class="classroom-status">
          <el-tag type="success">当前课堂码: {{ classroomStatus.classCode }}</el-tag>
          <el-button 
            size="small" 
            circle 
            type="primary"
            @click="navigator.clipboard.writeText(classroomStatus.classCode); ElMessage.success('课堂码已复制')">
            <el-icon><CopyDocument /></el-icon>
          </el-button>
          <span class="classroom-info">已连接学生: {{ classroomStatus.students.length || 0 }}人</span>
        </div>
        
        <div class="toolbar-center">
          <el-button-group>
            <el-button v-for="tool in tools" :key="tool.name" 
              :type="currentTool === tool.name ? 'primary' : ''" 
              size="small" 
              @click="selectTool(tool.name)"
              :title="tool.label"
              :disabled="!hasControl">
              <el-icon><component :is="tool.icon"></component></el-icon>
            </el-button>
          </el-button-group>
        </div>
        
        <div class="toolbar-right">
          <el-button @click="clearWhiteboard" size="small" type="danger" plain :disabled="!hasControl">
            清空白板
          </el-button>
          
          <!-- 控制权状态显示 -->
          <el-tag v-if="controlStatus.isControlGranted" type="warning">
            控制权: {{ controlStatus.controllingUserName || '学生' }}
            <span v-if="controlStatus.hasFullAccess">(完整权限)</span>
          </el-tag>
          <el-tag v-else type="success">
            控制权: 教师
          </el-tag>
          
          <!-- 教师控制权转移按钮 -->
          <template v-if="currentUser.role === '教师'">
            <el-button v-if="!controlStatus.isControlGranted" 
              @click="openControlTransferDialog" 
              size="small" 
              type="primary">
              转移控制权
            </el-button>
            <el-button v-else 
              @click="retrieveControl" 
              size="small" 
              type="warning">
              收回控制权
            </el-button>
          </template>
          
          <!-- 学生端控制权状态显示 -->
          <template v-else>
            <el-tag v-if="hasControl" type="success">
              您已获得控制权
              <span v-if="controlStatus.hasFullAccess">(完整权限)</span>
            </el-tag>
          </template>
        </div>
      </div>
      
      <!-- 幻灯片区域 -->
      <div class="slide-container">
        <div class="slide-controls">
          <el-button @click="prevSlide" :disabled="currentSlide <= 1 || !hasControl" type="primary" circle>
            <el-icon><arrow-left /></el-icon>
          </el-button>
          <span class="slide-indicator">{{ currentSlide }} / {{ totalSlides }}</span>
          <el-button @click="nextSlide" :disabled="currentSlide >= totalSlides || !hasControl" type="primary" circle>
            <el-icon><arrow-right /></el-icon>
          </el-button>
        </div>
        
        <!-- 幻灯片内容 -->
        <div class="slide-content">
          <div class="slide">
            <img v-if="pptInfo.images && pptInfo.images[currentSlide-1]" 
              :src="pptInfo.images[currentSlide-1]" 
              alt="幻灯片图片" />
            <div v-else class="slide-placeholder">
              <el-icon><Picture /></el-icon>
              <div>幻灯片 {{ currentSlide }}</div>
            </div>
          </div>
          
          <!-- 白板覆盖层 -->
          <canvas 
            ref="whiteboard" 
            class="whiteboard"
            @mousedown="handleMouseDown"
            @touchstart="handleTouchStart"
          ></canvas>
          
          <!-- 文本输入弹出框 -->
          <div v-if="isTyping" class="text-input-overlay" :style="{left: startX + 'px', top: startY + 'px'}">
            <el-input 
              v-model="textInput" 
              placeholder="输入文本" 
              size="small"
              autofocus
              @keyup.enter="commitTextInput"
            >
              <template #append>
                <el-button @click="commitTextInput">确定</el-button>
              </template>
            </el-input>
          </div>
        </div>
      </div>
      
      <!-- 底部工具栏 -->
      <div class="bottom-toolbar">
        <el-button-group>
          <el-button @click="openVoteCreator" type="primary" :disabled="!hasControl">
            <el-icon><ChatDotRound /></el-icon> 发起投票
          </el-button>
          <el-button type="success">
            <el-icon><VideoCamera /></el-icon> 录制课程
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <div v-show="sidebarVisible" class="sidebar">
      <div class="sidebar-tabs">
        <div 
          @click="switchSidePanel('students')" 
          :class="['sidebar-tab', {'active': activeSidePanel === 'students'}]"
        >
          学生列表
        </div>
        <div 
          @click="switchSidePanel('quickResponse')" 
          :class="['sidebar-tab', {'active': activeSidePanel === 'quickResponse'}]"
        >
          快速响应
        </div>
        <div
          @click="switchSidePanel('annotations')"
          :class="['sidebar-tab', {'active': activeSidePanel === 'annotations'}]"
        >
          课堂笔记
        </div>
        <div
          v-if="currentUser.role === '教师'"
          @click="switchSidePanel('questions')"
          :class="['sidebar-tab', {'active': activeSidePanel === 'questions'}]"
        >
          学生提问
          <el-badge v-if="newQuestionNotification > 0" :value="newQuestionNotification" class="question-badge" />
        </div>
      </div>
      
      <div class="sidebar-content">
        <!-- 学生列表 -->
        <div v-show="activeSidePanel === 'students'">
          <div class="panel-header">
            <h3>在线学生 ({{ onlineStudents.length }})</h3>
            <div v-if="classroomStatus.inClassroom" class="classroom-info-badge">
              <el-tag type="success" size="small">课堂已创建: {{ classroomStatus.classCode }}</el-tag>
            </div>
            <div v-else class="classroom-info-badge">
              <el-tag type="info" size="small">未创建课堂</el-tag>
              <el-button v-if="currentUser.role === '教师'" 
                type="primary" 
                size="small" 
                @click="openClassroomPanel"
                style="margin-left: 8px;">
                创建课堂
              </el-button>
            </div>
          </div>
          
          <ul class="student-list">
            <!-- 添加调试信息 -->
            <li v-if="classroomStatus.students.length > 0 && onlineStudents.length === 0" class="student-item debug">
              <div>
                <pre>课堂学生: {{ JSON.stringify(classroomStatus.students, null, 2) }}</pre>
                <pre>计算属性结果: {{ JSON.stringify(onlineStudents, null, 2) }}</pre>
              </div>
            </li>
            
            <li v-for="student in onlineStudents" :key="student.socketId || student.userId" class="student-item">
              <div class="student-info">
                <el-avatar :size="32" :src="student.avatar || 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'"></el-avatar>
                <span class="student-name">{{ student.userName || '未知学生' }}</span>
                <el-tag size="small" type="success" v-if="classroomStatus.inClassroom && student.joinedAt">已加入课堂</el-tag>
              </div>
              
              <div class="student-actions" v-if="currentUser.role === '教师' && classroomStatus.inClassroom">
                <el-tooltip content="转移PPT控制权" placement="top">
                <el-button 
                  v-if="!controlStatus.isControlGranted || controlStatus.controllingUserId !== student.userId"
                  @click="transferControlToStudent(student.userId, student.userName)"
                  type="primary" 
                  size="small"
                  plain>
                    <el-icon><Position /></el-icon> 授权
                </el-button>
                <el-button 
                  v-else
                  type="success" 
                    size="small">
                    <el-icon><Check /></el-icon> 已授权
                </el-button>
                </el-tooltip>
              </div>
            </li>
            
            <li v-if="onlineStudents.length === 0 && classroomStatus.students.length === 0" class="student-item empty">
              <el-empty description="暂无在线学生" :image-size="64">
                <template #description>
                  <div>
                    <p>暂无在线学生</p>
                    <p v-if="classroomStatus.inClassroom" style="font-size: 12px; color: #909399;">
                      请分享课堂码 <strong>{{ classroomStatus.classCode }}</strong> 给学生
                    </p>
                  </div>
                </template>
              </el-empty>
            </li>
          </ul>
        </div>
        
        <!-- 快速响应面板 -->
        <div v-show="activeSidePanel === 'quickResponse'">
          <QuickResponsePanel />
        </div>
        
        <!-- 课堂笔记面板 -->
        <div v-show="activeSidePanel === 'annotations'">
          <SlideAnnotationsPanel
            :slideId="currentSlide"
            :hasControl="hasControl"
          />
        </div>

        <!-- 学生提问面板 -->
        <div v-show="activeSidePanel === 'questions' && currentUser.role === '教师'">
          <div class="panel-header">
            <h3>学生提问 ({{ studentQuestions.length }})</h3>
          </div>

          <div v-if="studentQuestions.length === 0" class="empty-questions">
            <el-empty description="暂无学生提问" :image-size="60" />
          </div>

          <div v-else class="questions-list">
            <div v-for="question in studentQuestions" :key="question.id" class="question-item">
              <div class="question-header">
                <div class="student-info">
                  <el-avatar :size="24" :src="`https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png`"></el-avatar>
                  <span class="student-name">{{ question.userName }}</span>
                </div>
                <div class="question-time">
                  {{ new Date(question.time).toLocaleTimeString() }}
                </div>
              </div>

              <div class="question-content">
                {{ question.content }}
              </div>

              <div v-if="question.answered" class="question-answer">
                <div class="answer-label">教师回答：</div>
                <div class="answer-content">{{ question.answer }}</div>
                <div class="answer-time">
                  {{ new Date(question.answeredAt).toLocaleTimeString() }}
                </div>
              </div>

              <div v-else class="question-actions">
                <el-input
                  v-model="question.replyText"
                  type="textarea"
                  :rows="2"
                  placeholder="输入回答..."
                  class="answer-input"
                />
                <el-button
                  type="primary"
                  size="small"
                  @click="answerQuestion(question, question.replyText)"
                  :disabled="!question.replyText || !question.replyText.trim()"
                >
                  回答
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 投票创建对话框 -->
    <el-dialog
      v-model="isCreatingVote"
      title="创建投票"
      width="500px"
    >
      <el-form :model="voteForm" label-width="80px">
        <el-form-item label="标题">
          <el-input v-model="voteForm.title" placeholder="请输入投票标题"></el-input>
        </el-form-item>
        
        <el-form-item label="选项">
          <div v-for="(option, index) in voteForm.options" :key="index" class="vote-option">
            <el-input v-model="voteForm.options[index]" placeholder="请输入选项内容"></el-input>
            <el-button 
              v-if="voteForm.options.length > 2" 
              @click="removeOption(index)" 
              type="danger" 
              circle
              size="small"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          
          <el-button @click="addOption" type="primary" plain size="small">
            <el-icon><Plus /></el-icon> 添加选项
          </el-button>
        </el-form-item>
        
        <el-form-item label="投票设置">
          <el-checkbox v-model="voteForm.isMultiple">允许多选</el-checkbox>
          <el-checkbox v-model="voteForm.isAnonymous">匿名投票</el-checkbox>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="isCreatingVote = false">取消</el-button>
          <el-button type="primary" @click="createVote">创建投票</el-button>
        </span>
      </template>
    </el-dialog>
    
    <!-- 课堂管理对话框 -->
    <el-dialog
      v-model="showClassroomPanel"
      :title="classroomStatus.inClassroom ? '课堂管理' : '创建新课堂'"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="!isCreatingClassroom"
    >
      <div v-if="!classroomStatus.inClassroom" class="classroom-create">
        <p class="classroom-tip">创建新课堂后，系统将生成一个6位课堂码，您可以将课堂码分享给学生让他们加入课堂。</p>
        <div class="classroom-actions">
          <el-button 
            type="primary" 
            @click="handleCreateClassroom" 
            :loading="isCreatingClassroom"
            :disabled="isCreatingClassroom"
          >
            <el-icon v-if="!isCreatingClassroom"><Plus /></el-icon> 
            {{ isCreatingClassroom ? '正在创建课堂...' : '创建新课堂' }}
          </el-button>
        </div>
      </div>
      
      <div v-else class="classroom-manage">
        <div class="classroom-code-display">
          <h3>课堂码</h3>
          <div class="code-box">
            {{ classroomStatus.classCode }}
            <el-button 
              size="small" 
              circle
              @click="navigator.clipboard.writeText(classroomStatus.classCode); ElMessage.success('课堂码已复制')">
              <el-icon><CopyDocument /></el-icon>
            </el-button>
          </div>
          <p class="classroom-tip">请将此课堂码分享给学生，他们可以通过输入课堂码加入您的课堂。</p>
        </div>
        
        <div class="classroom-students">
          <h3>已连接学生 ({{ classroomStatus.students.length || 0 }})</h3>
          <el-empty v-if="!classroomStatus.students.length" description="暂无学生加入" :image-size="60" />
          <ul v-else class="student-list-mini">
            <li v-for="(student, index) in classroomStatus.students" :key="index" class="student-item-mini">
              {{ student.userName }}
            </li>
          </ul>
        </div>
        
        <div class="classroom-actions">
          <el-button type="danger" @click="handleEndClassroom">
            <el-icon><Close /></el-icon> 结束课堂
          </el-button>
        </div>
      </div>
    </el-dialog>
    
    <!-- PPT上传对话框 -->
    <el-dialog
      v-model="uploadPPTDialogVisible"
      title="上传PPT"
      width="500px"
    >
      <div class="upload-ppt-container">
        <div class="upload-description">
          <p>请选择要上传的PPT文件，支持.ppt和.pptx格式。</p>
          <p>上传后系统将自动转换为图片展示。</p>
        </div>
        
        <el-upload
          class="ppt-uploader"
          drag
          action="#"
          :auto-upload="false"
          :http-request="handlePPTUpload"
          :on-progress="handlePPTUploadProgress"
          :on-success="handlePPTUploadSuccess"
          :file-list="uploadedFiles"
          :limit="1"
          accept=".ppt,.pptx"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              仅支持 .ppt/.pptx 格式文件
            </div>
          </template>
        </el-upload>
        
        <el-progress 
          v-if="isUploading"
          :percentage="pptUploadProgress" 
          :stroke-width="18"
          status="success"
        ></el-progress>
      </div>
    </el-dialog>
  </div>
</template>

<style scoped>
.classroom-container {
  display: flex;
  height: calc(100vh - 64px);
  background-color: #f5f7fa;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
}

.toolbar-left,
.toolbar-right {
  flex: 0 0 auto;
}

.toolbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.slide-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

.slide-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
}

.slide-indicator {
  margin: 0 15px;
  font-size: 16px;
  font-weight: bold;
}

.slide-content {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
}

.slide {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
}

.slide img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.slide-placeholder {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #909399;
}

.slide-placeholder .el-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.whiteboard {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}

.bottom-toolbar {
  padding: 10px;
  background-color: #fff;
  border-top: 1px solid #e6e6e6;
  display: flex;
  justify-content: center;
}

.sidebar {
  width: 300px;
  border-left: 1px solid #e6e6e6;
  background-color: #fff;
  display: flex;
  flex-direction: column;
}

.sidebar-tabs {
  display: flex;
  border-bottom: 1px solid #e6e6e6;
}

.sidebar-tab {
  flex: 1;
  text-align: center;
  padding: 12px 0;
  cursor: pointer;
  transition: all 0.3s;
}

.sidebar-tab:hover {
  background-color: #f5f7fa;
}

.sidebar-tab.active {
  color: var(--el-color-primary);
  border-bottom: 2px solid var(--el-color-primary);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.panel-header {
  padding: 10px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  color: #303133;
}

.classroom-info-badge {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
}

.student-list {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: calc(100vh - 280px);
  overflow-y: auto;
}

.student-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 8px;
  transition: all 0.3s;
  background-color: #fff;
  border: 1px solid #ebeef5;
}

.student-item:hover {
  background-color: #f5f7fa;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.student-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.student-name {
  font-weight: 500;
  margin-right: 4px;
}

.student-actions {
  display: flex;
  gap: 6px;
}

.vote-option {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.vote-option .el-input {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .sidebar {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 10;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
  }
}

.classroom-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.classroom-info {
  font-size: 14px;
  color: #606266;
}

.classroom-create, .classroom-manage {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.classroom-tip {
  font-size: 14px;
  color: #606266;
  margin: 0;
  line-height: 1.5;
}

.classroom-actions {
  display: flex;
  justify-content: center;
  margin-top: 10px;
}

.classroom-code-display {
  text-align: center;
}

.classroom-code-display h3, .classroom-students h3 {
  font-size: 16px;
  margin: 0 0 10px;
  color: #303133;
}

.code-box {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: #ecf5ff;
  border: 1px solid #d9ecff;
  border-radius: 4px;
  padding: 15px;
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 2px;
  color: #409eff;
}

.student-list-mini {
  list-style: none;
  padding: 0;
  margin: 0;
  max-height: 150px;
  overflow-y: auto;
}

.student-item-mini {
  padding: 5px 10px;
  border-bottom: 1px solid #ebeef5;
}

.text-input-overlay {
  position: absolute;
  z-index: 20;
  min-width: 200px;
}

.toolbar-center .el-button-group .el-button.is-active,
.toolbar-center .el-button-group .el-button.is-active:focus,
.toolbar-center .el-button-group .el-button.is-active:hover {
  color: var(--el-button-hover-text-color);
  background-color: var(--el-button-hover-bg-color);
  border-color: var(--el-button-hover-border-color);
}

.student-item.empty {
  justify-content: center;
  padding: 20px 0;
}

/* PPT上传相关样式 */
.upload-ppt-container {
  padding: 10px 0;
}

.upload-description {
  margin-bottom: 20px;
}

.upload-description p {
  margin: 5px 0;
  color: #606266;
}

.ppt-uploader {
  width: 100%;
}

.ppt-uploader .el-upload {
  width: 100%;
}

.ppt-uploader .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-icon--upload {
  font-size: 48px;
  margin-bottom: 10px;
  color: #409EFF;
}

.el-upload__text {
  font-size: 16px;
  margin-top: 10px;
}

.el-upload__text em {
  color: #409EFF;
  font-style: normal;
}

.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
}

/* 学生提问面板样式 */
.question-badge {
  margin-left: 5px;
}

.empty-questions {
  padding: 20px 0;
  text-align: center;
}

.questions-list {
  max-height: calc(100vh - 300px);
  overflow-y: auto;
}

.question-item {
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 12px;
  transition: all 0.3s;
}

.question-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.question-header .student-info {
  display: flex;
  align-items: center;
  gap: 6px;
}

.question-header .student-name {
  font-weight: 500;
  font-size: 14px;
}

.question-time {
  font-size: 12px;
  color: #909399;
}

.question-content {
  background-color: #f5f7fa;
  padding: 8px 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 14px;
}

.question-answer {
  background-color: #e8f5e8;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid #67c23a;
}

.answer-label {
  font-size: 12px;
  color: #67c23a;
  font-weight: 500;
  margin-bottom: 4px;
}

.answer-content {
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 4px;
}

.answer-time {
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.question-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.answer-input {
  width: 100%;
}
</style> 