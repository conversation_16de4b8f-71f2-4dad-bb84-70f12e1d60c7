<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { addResource } from '../services/db/resourceService'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:visible', 'uploaded'])

// 上传表单
const uploadForm = reactive({
  title: '',
  description: '',
  type: 'ppt', // 默认为PPT课件
  subject: '',
  tags: [],
  file: null,
  coverImage: null
})

// 验证规则
const rules = {
  title: [
    { required: true, message: '请输入资源标题', trigger: 'blur' },
    { min: 2, max: 50, message: '标题长度应为2-50个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入资源描述', trigger: 'blur' },
    { max: 500, message: '描述最多500个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择资源类型', trigger: 'change' }
  ],
  subject: [
    { required: true, message: '请选择所属学科', trigger: 'change' }
  ],
  file: [
    { required: true, message: '请上传资源文件', trigger: 'change' }
  ]
}

// 资源类型
const resourceTypes = [
  { label: 'PPT课件', value: 'ppt' },
  { label: '教学视频', value: 'video' },
  { label: '教案', value: 'document' },
  { label: '习题', value: 'exercise' },
  { label: '其他资料', value: 'other' }
]

// 学科数据
const subjectOptions = [
  { label: '语文', value: 'chinese' },
  { label: '数学', value: 'math' },
  { label: '英语', value: 'english' },
  { label: '物理', value: 'physics' },
  { label: '化学', value: 'chemistry' },
  { label: '生物', value: 'biology' },
  { label: '历史', value: 'history' },
  { label: '地理', value: 'geography' },
  { label: '政治', value: 'politics' },
  { label: '信息技术', value: 'it' },
  { label: '综合', value: 'comprehensive' }
]

// 可选标签
const availableTags = [
  '小学', '初中', '高中', '大学', 
  '期中', '期末', '模拟考试', '知识点总结',
  '教学参考', '名师课件', '优质资源', '素材库'
]

// 表单引用
const uploadFormRef = ref(null)
// 上传中状态
const uploading = ref(false)

// 文件上传相关
const fileList = ref([])
const coverList = ref([])

// 文件类型映射
const fileTypeMapping = {
  'ppt': ['ppt', 'pptx'],
  'video': ['mp4', 'mov', 'avi', 'flv'],
  'document': ['doc', 'docx', 'pdf', 'txt'],
  'exercise': ['pdf', 'doc', 'docx', 'xls', 'xlsx'],
  'other': ['zip', 'rar', '7z', 'pdf']
}

// 获取上传类型接受的文件扩展名
const getAcceptedTypes = (type) => {
  const extensions = fileTypeMapping[type] || ['*']
  return extensions.map(ext => `.${ext}`).join(',')
}

// 文件上传前检查
const beforeFileUpload = (file) => {
  if (!file) return false;
  
  const selectedType = uploadForm.type
  const fileExtension = file.name.split('.').pop().toLowerCase()
  
  console.log('上传文件类型检查:', file.name, fileExtension, selectedType)
  
  // 检查文件类型是否匹配
  if (selectedType && fileTypeMapping[selectedType]) {
    if (!fileTypeMapping[selectedType].includes(fileExtension)) {
      ElMessage.error(`只能上传${fileTypeMapping[selectedType].join(', ')}格式的文件!`)
      return false
    }
  }
  
  // 文件大小限制 (50MB)
  const isLt50M = file.size / 1024 / 1024 < 50
  if (!isLt50M) {
    ElMessage.error('文件大小不能超过50MB!')
    return false
  }
  
  return true
}

// 封面图片上传前检查
const beforeCoverUpload = (file) => {
  // 只允许图片格式
  const isImage = /\.(jpg|jpeg|png|gif)$/i.test(file.name)
  if (!isImage) {
    ElMessage.error('封面只能上传图片格式!')
    return false
  }
  
  // 图片大小限制 (2MB)
  const isLt2M = file.size / 1024 / 1024 < 2
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过2MB!')
    return false
  }
  
  return true
}

// 文件上传成功处理
const handleFileSuccess = async (response, uploadFile) => {
  console.log('文件上传成功:', uploadFile)

  // 确保uploadFile存在且有raw属性
  if (uploadFile && uploadFile.raw) {
    uploadForm.file = uploadFile.raw

    // 如果是PPT文件，生成预览图
    let previewUrl = URL.createObjectURL(uploadFile.raw);
    if (uploadForm.type === 'ppt') {
      try {
        previewUrl = await generatePPTPreview(uploadFile.raw);
      } catch (error) {
        console.warn('生成PPT预览失败，使用默认图片:', error);
        previewUrl = '/default-ppt-preview.png'; // 使用默认预览图
      }
    }

    fileList.value = [{
      name: uploadFile.name || uploadFile.raw.name,
      size: uploadFile.raw.size,
      url: previewUrl
    }]
    ElMessage.success('文件选择成功')
  } else {
    ElMessage.error('文件处理失败')
  }
}

// 生成PPT预览图
const generatePPTPreview = async (file) => {
  return new Promise((resolve) => {
    // 创建一个canvas来生成PPT预览图
    const canvas = document.createElement('canvas');
    canvas.width = 400;
    canvas.height = 300;
    const ctx = canvas.getContext('2d');

    // 绘制PPT预览背景
    ctx.fillStyle = '#f8f9fa';
    ctx.fillRect(0, 0, 400, 300);

    // 绘制PPT图标
    ctx.fillStyle = '#d73527';
    ctx.fillRect(50, 50, 300, 200);

    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('PPT', 200, 140);

    ctx.font = '14px Arial';
    ctx.fillText(file.name, 200, 170);

    ctx.font = '12px Arial';
    ctx.fillText(`大小: ${(file.size / 1024 / 1024).toFixed(2)}MB`, 200, 190);

    // 转换为图片URL
    const imageUrl = canvas.toDataURL('image/png');
    resolve(imageUrl);
  });
}

// 封面上传成功处理
const handleCoverSuccess = (response, uploadFile) => {
  // 确保uploadFile存在且有raw属性
  if (uploadFile && uploadFile.raw) {
    uploadForm.coverImage = uploadFile.raw
    coverList.value = [{ 
      name: uploadFile.name || uploadFile.raw.name, 
      size: uploadFile.raw.size,
      url: URL.createObjectURL(uploadFile.raw) 
    }]
    ElMessage.success('封面选择成功')
  } else {
    ElMessage.error('封面处理失败')
  }
}

// 删除文件
const handleFileRemove = () => {
  uploadForm.file = null
  fileList.value = []
}

// 删除封面
const handleCoverRemove = () => {
  uploadForm.coverImage = null
  coverList.value = []
}

// 关闭对话框处理
const handleClose = () => {
  uploadFormRef.value?.resetFields()
  fileList.value = []
  coverList.value = []
  uploadForm.tags = []
  uploadForm.file = null
  uploadForm.coverImage = null
  uploadForm.type = 'ppt'
  emit('update:visible', false)
}

// 提交上传
const submitUpload = () => {
  uploadFormRef.value.validate(async (valid) => {
    if (valid) {
      if (!uploadForm.file) {
        ElMessage.warning('请上传资源文件')
        return
      }
      
      try {
        uploading.value = true
        console.log('开始上传文件:', uploadForm.file.name)
        
        // 获取文件大小文本
        const fileSize = uploadForm.file.size 
          ? (uploadForm.file.size / 1024 / 1024).toFixed(2) + 'MB'
          : '未知大小'
        
        // 处理封面图片
        let coverUrl = 'https://picsum.photos/300/200';
        if (uploadForm.coverImage) {
          coverUrl = `local://${uploadForm.coverImage.name}`;
        } else if (uploadForm.type === 'ppt') {
          // 为PPT生成特殊的预览封面
          coverUrl = await generatePPTPreview(uploadForm.file);
        }

        // 简化资源数据结构，避免序列化问题
        const resourceData = {
          title: uploadForm.title,
          description: uploadForm.description,
          type: uploadForm.type,
          subject: uploadForm.subject,
          tags: Array.isArray(uploadForm.tags) ? [...uploadForm.tags] : [],
          uploadTime: new Date().toISOString(),
          uploadedBy: localStorage.getItem('userId') || 'anonymous',
          fileName: uploadForm.file.name,
          fileType: uploadForm.file.type,
          fileUrl: `local://${uploadForm.file.name}`,
          coverUrl: coverUrl,
          fileSize: fileSize,
          downloads: 0,
          views: 0,
          // PPT特有属性
          isPPT: uploadForm.type === 'ppt',
          pptSlideCount: uploadForm.type === 'ppt' ? Math.floor(Math.random() * 15) + 5 : null,
          fileData: uploadForm.file // 保存文件引用用于后续处理
        }
        
        console.log('准备添加资源:', JSON.stringify(resourceData))
        
        // 添加资源到数据库
        const resourceId = await addResource(resourceData)
        
        // 添加成功
        if (resourceId) {
          console.log('资源添加成功，ID:', resourceId)
          ElMessage.success(`资源"${uploadForm.title}"上传成功`)
        uploading.value = false
        handleClose()
        emit('uploaded')
        } else {
          throw new Error('资源ID未返回')
        }
      } catch (error) {
        console.error('上传失败:', error)
        ElMessage.error(`资源上传失败: ${error.message || '未知错误'}`)
        uploading.value = false
      }
    } else {
      ElMessage.warning('请完善资源信息')
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="props.visible"
    title="上传课件资源"
    width="650px"
    :before-close="handleClose"
    append-to-body
  >
    <el-form
      ref="uploadFormRef"
      :model="uploadForm"
      :rules="rules"
      label-position="top"
    >
      <el-form-item label="资源类型" prop="type">
        <el-select v-model="uploadForm.type" placeholder="请选择资源类型" style="width: 100%">
          <el-option
            v-for="type in resourceTypes"
            :key="type.value"
            :label="type.label"
            :value="type.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="资源标题" prop="title">
        <el-input v-model="uploadForm.title" placeholder="请输入资源标题" />
      </el-form-item>

      <el-form-item label="所属学科" prop="subject">
        <el-select v-model="uploadForm.subject" placeholder="请选择所属学科" style="width: 100%">
          <el-option
            v-for="subject in subjectOptions"
            :key="subject.value"
            :label="subject.label"
            :value="subject.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="资源描述" prop="description">
        <el-input
          v-model="uploadForm.description"
          type="textarea"
          :rows="3"
          placeholder="请简要描述资源内容、适用范围等信息"
        />
      </el-form-item>

      <el-form-item label="资源标签">
        <el-select
          v-model="uploadForm.tags"
          multiple
          placeholder="选择适用的标签（可多选）"
          style="width: 100%"
        >
          <el-option
            v-for="tag in availableTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="上传文件" prop="file">
        <el-upload
          class="resource-upload"
          action="#"
          :auto-upload="false"
          :file-list="fileList"
          :on-change="(file) => { if(beforeFileUpload(file.raw)) handleFileSuccess(null, file) }"
          :on-remove="handleFileRemove"
          :limit="1"
          :multiple="false"
          :accept="getAcceptedTypes(uploadForm.type)"
          :on-exceed="() => { ElMessage.warning('只能上传一个文件') }"
          :show-file-list="true"
        >
          <el-button type="primary">选择文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              当前可上传{{ uploadForm.type === 'ppt' ? 'PPT(ppt/pptx)' : 
                        uploadForm.type === 'video' ? '视频(mp4/mov/avi)' : 
                        uploadForm.type === 'document' ? '文档(doc/docx/pdf)' : 
                        '文件' }}格式，且不超过50MB
            </div>
          </template>
        </el-upload>
      </el-form-item>

      <el-form-item label="资源封面">
        <el-upload
          class="cover-upload"
          action="#"
          :auto-upload="false"
          :file-list="coverList"
          :on-change="(file) => { if(beforeCoverUpload(file.raw)) handleCoverSuccess(null, file) }"
          :on-remove="handleCoverRemove"
          :limit="1"
          accept="image/*"
          :on-exceed="() => { ElMessage.warning('只能上传一张封面图片') }"
        >
          <el-button>选择封面图片</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只能上传jpg/png格式图片，且不超过2MB。不上传将使用默认封面
            </div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitUpload" :loading="uploading">
          上传资源
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.resource-upload,
.cover-upload {
  width: 100%;
}

.resource-upload .el-upload-list,
.cover-upload .el-upload-list {
  max-height: 150px;
  overflow-y: auto;
  margin-top: 10px;
}

.upload-preview {
  width: 100%;
  margin-top: 10px;
  display: flex;
  align-items: center;
}

.upload-preview img {
  max-width: 120px;
  max-height: 80px;
  object-fit: cover;
  border-radius: 4px;
}

.el-upload__tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}
</style> 